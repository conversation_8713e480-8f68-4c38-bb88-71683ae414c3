/**
 * Vista de detalle de un evento
 * Muestra la información detallada de un evento y opciones para gestionarla
 */
import { useState, useEffect } from 'react';
import Notification from '@/shared/components/ui/Notification'
import toast from '@/shared/components/ui/toast'
import { useParams, useNavigate } from 'react-router-dom'
import useNavigationContext from '@/shared/hooks/useNavigationContext'
import Card from '@/shared/components/ui/Card'
import Button from '@/shared/components/ui/Button'
import Avatar from '@/shared/components/ui/Avatar'
import Badge from '@/shared/components/ui/Badge'
import { formatDate, formatDateRange, formatTime } from '@/shared/utils/formatEventDate'
import {
    HiArrowLeft,
    HiPencil,
    HiUserGroup,
    HiCheck,
    HiX,
    HiCalendar,
    HiClock,
    HiLocationMarker,
    HiInformationCircle
} from 'react-icons/hi'
import { mockMeetings as mockEvents } from '@/mock/data/eventsData/index'
import type { Event, Department } from '../../types'
import StatusBadge from '../../components/StatusBadge'
import SelectSessionModal from '../../components/SelectSessionModal'
import QRModal from '../../components/QRModal'
import SessionStatusBadge from '../../components/SessionStatusBadge'
import { calculateEventProgress, getEventProgressMessage } from '../../utils/eventStatusUtils'
import { getDepartmentNameById } from '../../utils/departmentUtils'

/**
 * Componente para la vista de detalle de un evento
 */
const EventDetailView = () => {
    // Obtener el ID del evento de los parámetros de la URL
    const { id } = useParams<{ id: string }>()

    // Estado para almacenar los detalles del evento
    const [eventDetail, setEventDetail] = useState<Event | null>(null)

    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(true)

    // Hook de navegación
    const navigate = useNavigate()
    const [isQrModalOpen, setIsQrModalOpen] = useState(false)
    const [departments, setDepartments] = useState<Department[]>([])

    // Hook de navegación contextual
    const { navigateToAttendance } = useNavigationContext()

    // Cargar departamentos
    useEffect(() => {
        const fetchDepartments = async () => {
            try {
                // Simular llamada a la API para cargar departamentos
                // En una implementación real, esto vendría de un servicio
                const mockDepartments: Department[] = [
                    { id: 1, name: 'Presidencia' },
                    { id: 2, name: 'Secretaría' },
                    { id: 3, name: 'Tesorería' },
                    { id: 4, name: 'Educación' },
                    { id: 5, name: 'Misiones' }
                ]
                setDepartments(mockDepartments)
            } catch (error) {
                console.error('Error al cargar departamentos:', error)
            }
        }

        fetchDepartments()
    }, [])

    // Cargar los detalles del evento al montar el componente
    useEffect(() => {
        const fetchEventDetail = async () => {
            if (!id) return

            try {
                setLoading(true)

                // Simular llamada a la API
                setTimeout(() => {
                    const event = mockEvents.find(m => m.id.toString() === id)
                    setEventDetail(event || null)
                    setLoading(false)
                }, 500)
            } catch (error) {
                console.error('Error al cargar los detalles del evento:', error)
                setLoading(false)
            }
        }

        fetchEventDetail()
    }, [id])

    // Navegar a la vista de registro de asistencia
    const handleStartAttendance = () => {
        if (id && eventDetail) {
            // Si el evento tiene más de una sesión, mostrar modal de selección
            if (eventDetail.sessions && eventDetail.sessions.length > 1) {
                // TODO: Implementar modal de selección de sesión para EventDetail
                // Por ahora, usar la primera sesión
                const session = eventDetail.sessions[0]
                if (session.attendanceMode === 'kiosk') {
                    navigateToAttendance(
                        `/events/${id}/sessions/${session.id}/asistencia-rapida`,
                        'event-detail',
                        `/events/${id}`,
                        id,
                        session.id.toString()
                    )
                } else {
                    navigateToAttendance(
                        `/events/${id}/sessions/${session.id}/asistencia`,
                        'event-detail',
                        `/events/${id}`,
                        id,
                        session.id.toString()
                    )
                }
            } else {
                // Si tiene una sola sesión, ir directamente según la configuración de la sesión
                const session = eventDetail.sessions?.[0]
                if (session) {
                    if (session.attendanceMode === 'kiosk') {
                        navigateToAttendance(
                            `/events/${id}/sessions/${session.id}/asistencia-rapida`,
                            'event-detail',
                            `/events/${id}`,
                            id,
                            session.id.toString()
                        )
                    } else {
                        navigateToAttendance(
                            `/events/${id}/sessions/${session.id}/asistencia`,
                            'event-detail',
                            `/events/${id}`,
                            id,
                            session.id.toString()
                        )
                    }
                } else {
                    // Fallback: usar ruta antigua si no hay sesiones
                    console.warn('Evento sin sesiones, usando ruta de fallback')
                    navigateToAttendance(
                        `/events/${id}/attendance`,
                        'event-detail',
                        `/events/${id}`,
                        id
                    )
                }
            }
        }
    }

    // Navegar a la vista de edición del evento
    const handleEditEventFromDetail = () => {
        if (id) {
            navigate(`/events/${id}/edit`)
        }
    }

    // Volver a la lista de eventos
    const handleBack = () => {
        navigate('/events/list')
    }

    // Las funciones de formato se importan desde shared/utils

    if (loading) {
        return (
            <div className="flex justify-center items-center h-full">
                <p>Cargando detalles del evento...</p>
            </div>
        )
    }

    if (!eventDetail) {
        return (
            <div className="flex flex-col items-center justify-center h-full">
                <p className="text-lg mb-4">No se encontró el evento solicitado</p>
                <Button variant="solid" onClick={handleBack}>
                    Volver a la lista
                </Button>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-4">
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                    <Button
                        className="mr-4"
                        icon={<HiArrowLeft />}
                        onClick={handleBack}
                        variant="plain"
                    >
                        Volver al Listado
                    </Button>
                    <h1 className="text-2xl font-bold">
                        Detalles del Evento: {eventDetail.title}
                    </h1>
                </div>
            </div>

            {/* Sección de Información General */}
            <Card className="mb-6">
                <div className="p-6">
                    <h5 className="mb-4">Información General</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div className="mb-4">
                                <div className="font-semibold mb-1">Estado</div>
                                <StatusBadge status={eventDetail.eventStatus} />
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiCalendar className="mr-2" />
                                    {eventDetail.isMultiDay ? 'Fechas' : 'Fecha'}
                                </div>
                                <div>
                                    {eventDetail.isMultiDay && eventDetail.endDate ? (
                                        <>
                                            <div><strong>Inicio:</strong> {formatDate(eventDetail.date)}</div>
                                            <div><strong>Fin:</strong> {formatDate(eventDetail.endDate)}</div>
                                        </>
                                    ) : (
                                        formatDate(eventDetail.date)
                                    )}
                                </div>
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiClock className="mr-2" /> Horario
                                </div>
                                <div>
                                    {eventDetail.isMultiDay ? (
                                        <span className="flex flex-col">
                                            <span>
                                                <strong>Fecha y hora de inicio:</strong> {formatDate(eventDetail.date)}, {formatTime(eventDetail.startTime)}
                                            </span>
                                            {eventDetail.endDate && (
                                                <span>
                                                    <strong>Fecha y hora de fin:</strong> {formatDate(eventDetail.endDate)}, {formatTime(eventDetail.endTime)}
                                                </span>
                                            )}
                                        </span>
                                    ) : (
                                        `${formatTime(eventDetail.startTime)} - ${formatTime(eventDetail.endTime)}`
                                    )}
                                </div>
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiLocationMarker className="mr-2" /> Lugar
                                </div>
                                <div>{eventDetail.location}</div>
                            </div>
                        </div>

                        <div>
                            {eventDetail.subject && (
                                <div className="mb-4">
                                    <div className="font-semibold mb-1">Asunto/Grupo</div>
                                    <div>{eventDetail.subject}</div>
                                </div>
                            )}

                            {eventDetail.topic && (
                                <div className="mb-4">
                                    <div className="font-semibold mb-1">Tema</div>
                                    <div>{eventDetail.topic}</div>
                                </div>
                            )}

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiInformationCircle className="mr-2" /> Descripción
                                </div>
                                <div>{eventDetail.description || 'Sin descripción'}</div>
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1">Notificaciones</div>
                                <div>{eventDetail.sendNotifications ? 'Activadas' : 'Desactivadas'}</div>
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1">Gestión de Sesiones</div>
                                <div>
                                    {eventDetail.hasMultipleSessions ? (
                                        <span className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                                            Múltiples sesiones ({eventDetail.sessions?.length || 0})
                                        </span>
                                    ) : (
                                        <span className="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-full">
                                            Sesión única
                                        </span>
                                    )}
                                </div>
                            </div>

                            <div className="mb-4">
                                <div className="font-semibold mb-1 flex items-center">
                                    <HiInformationCircle className="mr-2" /> Departamento que Invita
                                </div>
                                <div>{getDepartmentNameById(eventDetail.invitingDepartment, departments)}</div>
                            </div>
                            {eventDetail.autoRegistration && (
                                <div className="mt-4">
                                    <Button variant="solid" onClick={() => setIsQrModalOpen(true)}>
                                        Generar QR/URL de Registro
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </Card>

            {/* Sección de Información de Múltiples Días (solo si aplica) */}
            {eventDetail.isMultiDay && (
                <Card className="mb-6">
                    <div className="p-6">
                        <h5 className="mb-4 flex items-center">
                            <HiCalendar className="mr-2" />
                            Información de Evento de Múltiples Días
                        </h5>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-center mb-2">
                                <HiInformationCircle className="text-blue-600 mr-2" />
                                <span className="font-semibold text-blue-800">
                                    Este evento se extiende por múltiples días
                                </span>
                            </div>
                            <div className="text-sm text-blue-700">
                                <div><strong>Fechas:</strong> {formatDateRange(eventDetail.date, eventDetail.endDate)}</div>
                                <div className="mt-2">
                                    Las sesiones individuales pueden tener horarios y métodos de asistencia específicos.
                                    Consulta la sección de sesiones para más detalles.
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>
            )}

            {/* Sección de Participantes */}
            <Card className="mb-6">
                <div className="p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h5>Participantes Convocados ({eventDetail.participantsInvited.length})</h5>

                        <div className="flex space-x-2">
                            {eventDetail.eventStatus !== 'cancelada' && (
                                <Button
                                    variant="solid"
                                    icon={<HiUserGroup />}
                                    onClick={handleStartAttendance}
                                >
                                    {eventDetail.eventStatus === 'completada' ? 'Ver Asistencia' : 'Registrar Asistencia'}
                                </Button>
                            )}

                            {eventDetail.eventStatus !== 'completada' && eventDetail.eventStatus !== 'cancelada' && (
                                <Button
                                    variant="solid"
                                    icon={<HiPencil />}
                                    onClick={handleEditEventFromDetail}
                                >
                                    Editar Evento
                                </Button>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {eventDetail.participantsInvited.map((participant) => {
                            // Buscar si hay registro de asistencia para este participante
                            const attendanceRecord = eventDetail.attendanceRecords?.find(
                                record => record.person.id === participant.id
                            )

                            // Determinar si asistió (desde el registro o desde la propiedad del participante)
                            const attended = attendanceRecord?.attended ?? participant.attended

                            return (
                                <div
                                    key={participant.id}
                                    className="flex items-center p-3 border rounded-lg"
                                >
                                    <Avatar
                                        src={typeof participant.avatar === 'string' ? participant.avatar : participant.avatar?.url}
                                        size={40}
                                        className="mr-3"
                                    />
                                    <div className="flex-grow">
                                        <div className="font-medium">
                                            {participant.firstName} {participant.lastName}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {participant.ecclesiasticalRole || participant.email}
                                        </div>
                                    </div>

                                    {eventDetail.eventStatus === 'completada' && attended !== null && (
                                        <div className="ml-2">
                                            {attended ? (
                                                <Badge className="bg-emerald-100 text-emerald-800 border border-emerald-200 px-2.5 py-1 rounded-full text-xs font-medium flex items-center">
                                                    <HiCheck className="mr-1.5" /> Asistió
                                                </Badge>
                                            ) : (
                                                <Badge className="bg-red-100 text-red-800 border border-red-200 px-2.5 py-1 rounded-full text-xs font-medium flex items-center">
                                                    <HiX className="mr-1.5" /> No asistió
                                                </Badge>
                                            )}
                                        </div>
                                    )}
                                </div>
                            )
                        })}
                    </div>
                </div>
            </Card>

            {/* Sección de Sesiones (solo si tiene múltiples sesiones) */}
            {eventDetail.hasMultipleSessions && eventDetail.sessions && eventDetail.sessions.length > 1 && (
                <Card className="mb-6">
                    <div className="p-6">
                        <div className="flex justify-between items-center mb-4">
                            <h5>Sesiones de Asistencia ({eventDetail.sessions.length})</h5>
                            <div className="text-sm text-gray-600">
                                {getEventProgressMessage(eventDetail)}
                            </div>
                        </div>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Fecha
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Comentario
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Modo de Asistencia
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Estado
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Tipo
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {eventDetail.sessions.map((session, index) => (
                                        <tr key={session.id}>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {formatDate(session.date)}
                                            </td>
                                            <td className="px-6 py-4 text-sm text-gray-900">
                                                <div className="flex items-center">
                                                    {session.comment}
                                                    {session.isDefault && (
                                                        <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                                            Por defecto
                                                        </span>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                    session.attendanceMode === 'kiosk'
                                                        ? 'bg-green-100 text-green-800'
                                                        : 'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {session.attendanceMode === 'kiosk' ? 'Kiosco Rápido' : 'Manual'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <SessionStatusBadge
                                                    status={session.status}
                                                    size="sm"
                                                />
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {session.isDefault ? 'Principal' : 'Adicional'}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </Card>
            )}

            {/* Modal de código QR */}
            {eventDetail && (
                <QRModal
                    isOpen={isQrModalOpen}
                    onClose={() => setIsQrModalOpen(false)}
                    event={eventDetail}
                    title="Compartir Registro de Evento"
                />
            )}
        </div>
    )
}

export default EventDetailView
