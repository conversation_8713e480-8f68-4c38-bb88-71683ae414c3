# Refactorización de Participantes - Resumen de Cambios

## Contexto
Se realizó una refactorización completa del sistema de participantes para alinearlo con la estructura real de la API de Strapi v5, separando correctamente los conceptos de "usuarios" (cuentas del sistema) y "participantes" (asistentes a eventos).

## Cambios Principales

### 1. Estructura de Datos Dual
La nueva implementación maneja dos estructuras diferentes según el contexto:

#### Respuestas GET (Objetos Completos)
```typescript
{
  id: 1,
  documentId: "doc-1",
  firstName: "<PERSON>",
  lastName: "<PERSON>",
  email: "<EMAIL>",
  fieldAssignment: {
    id: 1,
    documentId: "field-1",
    name: "Asociación Central",
    acronym: "ACD",
    type: "Asociación"
  },
  zoneAssignment: {
    id: 1,
    documentId: "zone-1",
    name: "Zona Central",
    field: { ... }
  }
  // ... más relaciones jerárquicas
}
```

#### Requests POST/PUT (Solo IDs)
```typescript
{
  firstName: "<PERSON>",
  lastName: "<PERSON>",
  email: "<EMAIL>",
  fieldAssignment: "field-1",  // Solo documentId
  zoneAssignment: "zone-1",    // Solo documentId
  districtAssignment: "district-1",
  churchAssignment: "church-1"
}
```

### 2. Archivos Modificados

#### `src/features/events/types/participant.ts`
- **Actualizado**: Interface `Participant` para manejar estructura dual
- **Agregado**: Interfaces jerárquicas (`Field`, `Zone`, `District`, `Church`)
- **Agregado**: Interfaces de request (`CreateParticipantRequest`, `UpdateParticipantRequest`)
- **Agregado**: Funciones utilitarias para transformación de datos

#### `src/features/events/services/ParticipantsService.ts`
- **Actualizado**: Método `getParticipants` con populate específico para relaciones
- **Actualizado**: Filtros usando `documentId` en lugar de `id`
- **Actualizado**: Método `createParticipant` para enviar solo IDs en requests
- **Actualizado**: Método `findParticipantByEmail` con populate completo
- **Agregado**: Enriquecimiento automático de participantes con IDs de compatibilidad

#### `src/features/events/views/EventForm/EventFormView.tsx`
- **Actualizado**: Filtrado de participantes para usar objetos jerárquicos
- **Actualizado**: Creación de participantes con nueva estructura de request
- **Actualizado**: Manejo de participantes temporales con IDs jerárquicos

### 3. Estrategia de Populate

#### Populate Específico para Relaciones
```typescript
populate: {
  avatar: true,
  userAccount: {
    populate: ['avatar']
  },
  fieldAssignment: {
    populate: ['*']
  },
  zoneAssignment: {
    populate: ['field']
  },
  districtAssignment: {
    populate: ['zone']
  },
  churchAssignment: {
    populate: ['district']
  }
}
```

### 4. Funciones Utilitarias

#### `enrichParticipantWithNames`
Enriquece participantes con IDs de compatibilidad:
```typescript
export function enrichParticipantWithNames(participant: Participant): Participant {
    return {
        ...participant,
        fieldAssignmentId: participant.fieldAssignment?.documentId || participant.fieldAssignmentId,
        zoneAssignmentId: participant.zoneAssignment?.documentId || participant.zoneAssignmentId,
        districtAssignmentId: participant.districtAssignment?.documentId || participant.districtAssignmentId,
        churchAssignmentId: participant.churchAssignment?.documentId || participant.churchAssignmentId,
    }
}
```

### 5. Compatibilidad con Strapi v5

#### Uso de documentId
- **URLs de actualización**: Usan `documentId` en la URL
- **Filtros**: Usan `documentId` para filtrar relaciones
- **Relaciones**: Strapi maneja automáticamente las relaciones cuando se proporcionan IDs

#### Formato de Request
```typescript
// Strapi v5 requiere envolver datos en objeto "data"
const strapiPayload = {
    data: participantPayload
}
```

### 6. Pruebas Unitarias

Se crearon pruebas completas en `src/features/events/services/__tests__/ParticipantsService.test.ts`:
- ✅ Obtención de participantes con populate
- ✅ Aplicación de filtros con documentId
- ✅ Creación de participantes con IDs
- ✅ Búsqueda por email con populate completo
- ✅ Manejo de casos de error

## Beneficios de la Refactorización

### 1. Alineación con API Real
- Estructura de datos coincide exactamente con Strapi v5
- Manejo correcto de relaciones jerárquicas
- Uso apropiado de documentId para actualizaciones

### 2. Separación de Responsabilidades
- Usuarios: Cuentas del sistema con autenticación
- Participantes: Asistentes a eventos con información jerárquica

### 3. Mejor Rendimiento
- Populate específico reduce transferencia de datos innecesarios
- Filtros optimizados usando documentId
- Enriquecimiento automático para compatibilidad

### 4. Mantenibilidad
- Código más limpio y organizado
- Funciones utilitarias reutilizables
- Pruebas unitarias completas

## Próximos Pasos

1. **Actualizar Componentes UI**: Modificar componentes que muestran información jerárquica
2. **Implementar Endpoints Jerárquicos**: Crear servicios para obtener fields, zones, districts, churches
3. **Optimizar Filtros**: Implementar filtros en cascada para mejor UX
4. **Documentar API**: Actualizar documentación de endpoints y estructuras

## Notas Técnicas

- **Compatibilidad**: Se mantienen métodos deprecated para transición gradual
- **Validación**: Las pruebas confirman que la estructura funciona correctamente
- **Flexibilidad**: El sistema maneja tanto objetos completos como IDs según el contexto
- **Escalabilidad**: La estructura permite agregar fácilmente nuevos niveles jerárquicos
