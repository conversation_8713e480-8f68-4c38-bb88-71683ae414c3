# Implementación Técnica - Búsqueda de Participantes

## Resumen de Cambios

Esta documentación describe la migración de la funcionalidad de búsqueda de participantes desde datos mockeados hacia la integración con la API real de Strapi v5.

### Archivos Modificados/Creados

#### Nuevos Archivos
- `src/features/events/services/HierarchicalDataService.ts`
- `docs/features/events/participant-search.md`
- `docs/development/participant-search-implementation.md`

#### Archivos Modificados
- `src/features/events/services/ParticipantsService.ts`
- `src/features/events/types/participant.ts`
- `src/features/events/types/index.ts`
- `src/shared/constants/api.constant.ts`
- `src/features/events/views/EventForm/EventFormView.tsx`

## Detalles de Implementación

### 1. HierarchicalDataService

**Propósito**: Centralizar la gestión de datos jerárquicos eclesiásticos.

**Características técnicas**:
- Uso de `ApiService.fetchData()` para consistencia
- Transformación de datos de Strapi v5 al formato del frontend
- Manejo de errores con logging detallado
- Soporte para documentId de Strapi v5

**Métodos implementados**:
```typescript
// Obtener datos completos
async getHierarchicalData(): Promise<HierarchicalFilterData>

// Obtener entidades específicas
async getFields(): Promise<Field[]>
async getZonesByField(fieldId: string | number): Promise<Zone[]>
async getDistrictsByZone(zoneId: string | number): Promise<District[]>
async getChurchesByDistrict(districtId: string | number): Promise<Church[]>

// Obtener roles únicos
async getEcclesiasticalRoles(): Promise<string[]>
```

### 2. Actualización de ParticipantsService

**Nuevos métodos agregados**:

```typescript
// Búsqueda con filtros jerárquicos combinados
async searchParticipantsWithFilters(
    searchTerm?: string,
    hierarchicalFilters?: {
        fieldId?: string | number | null
        zoneId?: string | number | null
        districtId?: string | number | null
        churchId?: string | number | null
        ecclesiasticalRole?: string | null
    }
): Promise<Participant[]>

// Obtener roles eclesiásticos (delegado a HierarchicalDataService)
async getEcclesiasticalRoles(): Promise<string[]>

// Actualización del método existente para usar API real
async getHierarchicalData(): Promise<HierarchicalFilterData>
```

### 3. Tipos y Constantes

**Cargos Eclesiásticos**:
```typescript
export const ECCLESIASTICAL_ROLES = {
    PASTOR: 'Pastor',
    PASTOR_DISTRITAL: 'Pastor Distrital',
    PASTOR_ASOCIADO: 'Pastor Asociado',
    ANCIANO: 'Anciano',
    ANCIANA: 'Anciana',
    DIACONO: 'Diácono',
    DIACONISA: 'Diaconisa',
    DEPARTAMENTAL: 'Departamental',
    DIRECTOR_DEPARTAMENTAL: 'Director Departamental',
    SECRETARIO: 'Secretario',
    TESORERO: 'Tesorero',
    MIEMBRO: 'Miembro',
    INVITADO: 'Invitado',
    LIDER_JOVEN: 'Líder Joven',
    MAESTRO_ESCUELA_SABATICA: 'Maestro Escuela Sabática',
    COORDINADOR: 'Coordinador'
} as const
```

**Nuevos Endpoints**:
```typescript
// En api.constant.ts
HIERARCHICAL_DATA: `${apiConfig.apiPrefix}/events/hierarchical-data`,

FIELDS: {
    BASE: `${apiConfig.apiPrefix}/fields`,
    BY_ID: (id: string | number) => `${apiConfig.apiPrefix}/fields/${id}`,
},

ZONES: {
    BASE: `${apiConfig.apiPrefix}/zones`,
    BY_FIELD: (fieldId: string | number) => `${apiConfig.apiPrefix}/zones?filters[field][documentId][$eq]=${fieldId}`,
},

DISTRICTS: {
    BASE: `${apiConfig.apiPrefix}/districts`,
    BY_ZONE: (zoneId: string | number) => `${apiConfig.apiPrefix}/districts?filters[zone][documentId][$eq]=${zoneId}`,
},

CHURCHES: {
    BASE: `${apiConfig.apiPrefix}/churches`,
    BY_DISTRICT: (districtId: string | number) => `${apiConfig.apiPrefix}/churches?filters[district][documentId][$eq]=${districtId}`,
},

PARTICIPANT_SEARCH: {
    BASE: `${apiConfig.apiPrefix}/participants/search`,
    ECCLESIASTICAL_ROLES: `${apiConfig.apiPrefix}/participants?fields[0]=ecclesiasticalRole`,
}
```

### 4. Actualización del Componente EventFormView

**Estados agregados**:
```typescript
// Datos jerárquicos reales
const [hierarchicalData, setHierarchicalData] = useState<HierarchicalFilterData | null>(null)
const [isLoadingHierarchicalData, setIsLoadingHierarchicalData] = useState(false)
```

**Carga inicial de datos**:
```typescript
useEffect(() => {
    const loadHierarchicalData = async () => {
        setIsLoadingHierarchicalData(true)
        try {
            const data = await ParticipantsService.getHierarchicalData()
            setHierarchicalData(data)
        } catch (error) {
            console.error('Error al cargar datos jerárquicos:', error)
            // Fallback a datos mockeados
            setHierarchicalData({
                fields: mockHierarchicalData.fields,
                zones: mockHierarchicalData.zones,
                districts: mockHierarchicalData.districts,
                churches: mockHierarchicalData.churches,
                ecclesiasticalRoles: mockHierarchicalData.ecclesiasticalRoles
            })
        } finally {
            setIsLoadingHierarchicalData(false)
        }
    }

    loadHierarchicalData()
}, [])
```

**Actualización de dropdowns**:
Todos los dropdowns ahora usan `hierarchicalData` en lugar de `mockHierarchicalData`:

```typescript
// Ejemplo: Dropdown de campos
<Select
    options={[
        { value: null, label: 'Todos los Campos' },
        ...(hierarchicalData?.fields || []).map(field => ({
            value: field.id,
            label: field.name
        }))
    ]}
    value={selectedField === null ? 
        { value: null, label: 'Todos los Campos' } : 
        (hierarchicalData?.fields || [])
            .filter(field => field.id === selectedField)
            .map(field => ({ value: field.id, label: field.name }))[0]
    }
    onChange={option => handleFieldChange(option?.value !== undefined ? option.value : null)}
    placeholder="Seleccionar Campo"
    isLoading={isLoadingHierarchicalData}
/>
```

**Función de búsqueda actualizada**:
```typescript
const handleApplyFiltersInModal = async () => {
    setLoadingParticipants(true)

    try {
        const hierarchicalFilters = {
            fieldId: selectedField,
            zoneId: selectedZone,
            districtId: selectedDistrict,
            churchId: selectedChurch,
            ecclesiasticalRole: typeof selectedRole === 'string' ? selectedRole : null
        }

        const participants = await ParticipantsService.searchParticipantsWithFilters(
            searchTerm || undefined,
            hierarchicalFilters
        )

        console.log('Participantes obtenidos de la API:', participants.length)
        setFilteredModalParticipants(participants)
    } catch (error) {
        console.error('Error al cargar participantes:', error)
        // Fallback a datos mockeados con filtrado local
        // ... lógica de fallback
    } finally {
        setLoadingParticipants(false)
    }
}
```

## Consideraciones de Compatibilidad

### Strapi v5
- Uso de `documentId` en lugar de `id` para filtros de relaciones
- Sintaxis de populate específica para evitar sobrecarga
- Manejo de respuestas con estructura `{ data: [...] }`

### Backward Compatibility
- Mantenimiento de interfaces existentes
- Fallback a datos mockeados en caso de error
- Preservación de funcionalidad existente

### Error Handling
- Logging detallado para debugging
- Notificaciones de error al usuario
- Fallbacks automáticos a datos locales

## Testing

### Casos de Prueba Implementados
1. **Carga de datos jerárquicos**: Verificar carga exitosa y fallback
2. **Filtrado en cascada**: Probar secuencia completa de filtros
3. **Búsqueda combinada**: Texto libre + filtros estructurales
4. **Manejo de errores**: API no disponible, datos incompletos
5. **Estados de carga**: Indicadores visuales correctos

### Casos de Prueba Pendientes
1. **Rendimiento**: Pruebas con grandes datasets
2. **Concurrencia**: Múltiples usuarios simultáneos
3. **Conectividad**: Comportamiento con conexión intermitente
4. **Validación**: Datos malformados de la API

## Deployment

### Requisitos Previos
1. Backend con endpoints implementados
2. Base de datos con estructura jerárquica poblada
3. Configuración de CORS para nuevos endpoints

### Variables de Configuración
- `API_PREFIX`: Prefijo de la API (configurado en `api.config.ts`)
- Endpoints específicos en `api.constant.ts`

### Monitoreo
- Logs de errores en servicios
- Métricas de rendimiento de búsqueda
- Uso de fallbacks a datos mockeados

## Próximos Pasos

### Mejoras Planificadas
1. **Paginación**: Implementar para grandes datasets
2. **Caché**: Datos jerárquicos en localStorage
3. **Debouncing**: Búsqueda por texto con retraso
4. **Lazy Loading**: Carga bajo demanda de datos

### Refactoring Futuro
1. **Custom Hooks**: Extraer lógica de filtrado
2. **Context API**: Compartir datos jerárquicos globalmente
3. **Memoización**: Optimizar re-renders
4. **Virtualization**: Para listas muy largas

## Troubleshooting

### Problemas Comunes
1. **Datos no cargan**: Verificar endpoints y CORS
2. **Filtros no funcionan**: Revisar documentId vs id
3. **Rendimiento lento**: Implementar paginación
4. **Errores de tipos**: Actualizar interfaces TypeScript

### Debugging
1. Revisar logs en consola del navegador
2. Verificar Network tab para llamadas API
3. Comprobar estructura de datos en respuestas
4. Validar configuración de endpoints
