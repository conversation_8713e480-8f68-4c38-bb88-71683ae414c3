import ApiService from '@/shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import HierarchicalDataService from './HierarchicalDataService'
import type {
    Participant,
    CreateParticipantRequest,
    UpdateParticipantRequest,
    ParticipantFilters,
    ParticipantResponse,
    SingleParticipantResponse,
    ParticipantValidationResult,
    ParticipantValidationData,
    HierarchicalFilterData,
    Field,
    Zone,
    District,
    Church
} from '../types'
import {
    enrichParticipantWithNames
} from '../types/participant'

/**
 * Servicio para gestionar participantes de eventos con estructura jerárquica
 *
 * Este servicio maneja la comunicación con la API de Strapi v5 para operaciones CRUD
 * de participantes, implementando una estrategia de populate específico para optimizar
 * el rendimiento y manejar correctamente la estructura jerárquica organizacional.
 *
 * Características principales:
 * - Populate estratégico para evitar sobrecarga de datos
 * - Manejo dual de estructuras (objetos completos en GET, IDs en POST/PUT)
 * - Filtros optimizados usando documentId para Strapi v5
 * - Enriquecimiento automático de datos para compatibilidad
 */
const ParticipantsService = {
    /**
     * Obtiene todos los participantes del sistema con populate de relaciones jerárquicas
     *
     * Este método implementa una estrategia de populate específico que carga solo
     * las relaciones necesarias para mostrar la información jerárquica completa
     * sin sobrecargar la transferencia de datos.
     *
     * @param filters - Filtros opcionales para la búsqueda de participantes
     * @param filters.search - Término de búsqueda para nombre, apellido, email o rol
     * @param filters.fieldAssignmentId - ID del campo/asociación para filtrar
     * @param filters.zoneAssignmentId - ID de la zona para filtrar
     * @param filters.districtAssignmentId - ID del distrito para filtrar
     * @param filters.churchAssignmentId - ID de la iglesia para filtrar
     * @param filters.ecclesiasticalRole - Rol eclesiástico específico para filtrar
     * @returns Promesa con la lista de participantes enriquecidos con objetos jerárquicos completos
     * @throws Error si no se pueden obtener los participantes de la API
     */
    async getParticipants(filters?: ParticipantFilters): Promise<Participant[]> {
        try {
            // Construir parámetros de consulta con populate específico para las relaciones jerárquicas
            // Esta estrategia evita el uso de 'populate: *' que puede causar sobrecarga de datos
            // y problemas de rendimiento al cargar relaciones innecesarias
            const params: Record<string, any> = {
                populate: {
                    // ✅ CORREGIDO: Estructura de populate simplificada compatible con Strapi v5
                    avatar: true,
                    userAccount: {
                        populate: ['avatar']
                    },
                    // ✅ SIMPLIFICADO: Populate básico para relaciones jerárquicas
                    // Eliminamos ['*'] y populate anidado que causa errores de validación
                    fieldAssignment: true,
                    zoneAssignment: true,
                    districtAssignment: true,
                    churchAssignment: true
                }
            }

            // Agregar filtros si se proporcionan por el usuario
            // Los filtros se construyen usando la sintaxis de Strapi v5 para consultas
            if (filters) {
                const strapiFilters: Record<string, any> = {}

                // Filtro de búsqueda por texto libre
                // Busca en múltiples campos usando operador $or para mayor flexibilidad
                if (filters.search) {
                    strapiFilters.$or = [
                        { firstName: { $containsi: filters.search } },      // Nombre (insensible a mayúsculas)
                        { lastName: { $containsi: filters.search } },       // Apellido (insensible a mayúsculas)
                        { email: { $containsi: filters.search } },          // Email (insensible a mayúsculas)
                        { ecclesiasticalRole: { $containsi: filters.search } } // Rol eclesiástico
                    ]
                }

                // Filtros por asignaciones jerárquicas usando documentId para Strapi v5
                // IMPORTANTE: Strapi v5 requiere usar 'documentId' en lugar de 'id' para filtros de relaciones
                if (filters.fieldAssignmentId) {
                    strapiFilters.fieldAssignment = { documentId: filters.fieldAssignmentId }
                }
                if (filters.zoneAssignmentId) {
                    strapiFilters.zoneAssignment = { documentId: filters.zoneAssignmentId }
                }
                if (filters.districtAssignmentId) {
                    strapiFilters.districtAssignment = { documentId: filters.districtAssignmentId }
                }
                if (filters.churchAssignmentId) {
                    strapiFilters.churchAssignment = { documentId: filters.churchAssignmentId }
                }

                // Filtro por rol eclesiástico específico (búsqueda exacta)
                if (filters.ecclesiasticalRole) {
                    strapiFilters.ecclesiasticalRole = { $eq: filters.ecclesiasticalRole }
                }

                // Solo agregar filtros a los parámetros si hay al menos uno definido
                if (Object.keys(strapiFilters).length > 0) {
                    params.filters = strapiFilters
                }
            }

            const response = await ApiService.fetchData<ParticipantResponse>({
                url: API_ENDPOINTS.PARTICIPANTS.BASE,
                method: 'get',
                params
            })

            // Enriquecer participantes con IDs para compatibilidad
            const participants = response.data?.data || []
            return participants.map(enrichParticipantWithNames)
        } catch (error) {
            console.error('Error al obtener participantes:', error)
            throw new Error('No se pudieron cargar los participantes')
        }
    },

    /**
     * Valida si un participante existe en el sistema antes de crearlo
     * @param eventId - ID del evento
     * @param participantData - Datos del participante a validar
     * @returns Resultado de la validación
     */
    async validateParticipant(eventId: string | number, participantData: ParticipantValidationData): Promise<ParticipantValidationResult> {
        try {
            const response = await ApiService.fetchData<ParticipantValidationResult>({
                url: API_ENDPOINTS.EVENTS.VALIDATE_PARTICIPANT(eventId),
                method: 'post',
                data: { participantData }
            })

            return response.data
        } catch (error) {
            console.error('Error al validar participante:', error)
            throw new Error('No se pudo validar el participante')
        }
    },

    /**
     * Crea un nuevo participante en el sistema usando la API de Strapi v5
     *
     * Este método maneja la creación de participantes enviando solo los IDs/documentIds
     * de las relaciones jerárquicas, permitiendo que Strapi maneje automáticamente
     * la creación de las relaciones en la base de datos.
     *
     * @param participantData - Datos del participante a crear
     * @param participantData.firstName - Nombre del participante
     * @param participantData.lastName - Apellido del participante
     * @param participantData.email - Email único del participante
     * @param participantData.phone - Teléfono opcional del participante
     * @param participantData.ecclesiasticalRole - Rol eclesiástico (por defecto: 'Miembro')
     * @param participantData.fieldAssignment - DocumentId del campo/asociación asignado
     * @param participantData.zoneAssignment - DocumentId de la zona asignada
     * @param participantData.districtAssignment - DocumentId del distrito asignado
     * @param participantData.churchAssignment - DocumentId de la iglesia asignada
     * @param participantData.userAccount - DocumentId de la cuenta de usuario asociada (opcional)
     * @returns Promesa con el participante creado incluyendo las relaciones pobladas
     * @throws Error si no se puede crear el participante o si hay errores de validación
     */
    async createParticipant(participantData: CreateParticipantRequest): Promise<Participant> {
        try {
            // Preparar datos para el endpoint de participantes
            // IMPORTANTE: Para POST requests, Strapi v5 requiere solo IDs/documentIds para las relaciones,
            // no los objetos completos. Strapi se encarga automáticamente de crear las relaciones.
            const participantPayload = {
                firstName: participantData.firstName,
                lastName: participantData.lastName,
                email: participantData.email,
                phone: participantData.phone || '',
                ecclesiasticalRole: participantData.ecclesiasticalRole || 'Miembro',
                // Relaciones jerárquicas - solo documentIds para que Strapi maneje las relaciones
                fieldAssignment: participantData.fieldAssignment || null,
                zoneAssignment: participantData.zoneAssignment || null,
                districtAssignment: participantData.districtAssignment || null,
                churchAssignment: participantData.churchAssignment || null,
                userAccount: participantData.userAccount || null,
            }

            // Strapi v5 requiere que los datos estén envueltos en un objeto "data"
            const strapiPayload = {
                data: participantPayload
            }

            console.log('Datos enviados para crear participante (formato Strapi v5):', strapiPayload)

            // Usar el endpoint de participantes
            const response = await ApiService.fetchData<SingleParticipantResponse>({
                url: API_ENDPOINTS.PARTICIPANTS.BASE,
                method: 'post',
                data: strapiPayload
            })

            console.log('Participante creado exitosamente:', response.data)
            return response.data.data
        } catch (error: unknown) {
            console.error('Error detallado al crear participante:', error)
            // Proporcionar más información sobre el error
            if (error && typeof error === 'object' && 'response' in error) {
                const apiError = error as { response?: { data?: any } }
                if (apiError.response?.data) {
                    console.error('Respuesta del servidor:', apiError.response.data)
                    const errorMessage = apiError.response.data.error?.message ||
                                       apiError.response.data.message ||
                                       'Error desconocido'
                    throw new Error(`Error del servidor: ${errorMessage}`)
                }
            }
            throw new Error('No se pudo crear el participante')
        }
    },



    /**
     * Busca un participante por email
     * @param email - Email del participante a buscar
     * @returns Participante encontrado o null
     */
    async findParticipantByEmail(email: string): Promise<Participant | null> {
        try {
            const response = await ApiService.fetchData<ParticipantResponse>({
                url: API_ENDPOINTS.PARTICIPANTS.BASE,
                method: 'get',
                params: {
                    filters: {
                        email: { $eq: email }
                    },
                    populate: {
                        // ✅ SIMPLIFICADO: Estructura de populate compatible con Strapi v5
                        // Eliminamos populate anidado complejo que causa errores de validación
                        avatar: true,
                        userAccount: {
                            populate: ['avatar']
                        },
                        // ✅ CORREGIDO: Populate básico para relaciones jerárquicas
                        fieldAssignment: true,
                        zoneAssignment: true,
                        districtAssignment: true,
                        churchAssignment: true
                    }
                }
            })

            const participants = response.data?.data || []
            if (participants.length > 0) {
                return enrichParticipantWithNames(participants[0])
            }
            return null
        } catch (error) {
            console.error('Error al buscar participante por email:', error)
            return null
        }
    },

    // Métodos de compatibilidad para código existente
    /**
     * @deprecated Use getParticipants instead
     */
    async getUsers(filters?: ParticipantFilters): Promise<Participant[]> {
        return this.getParticipants(filters)
    },

    /**
     * @deprecated Use findParticipantByEmail instead
     */
    async findUserByEmail(email: string): Promise<Participant | null> {
        return this.findParticipantByEmail(email)
    },

    /**
     * @deprecated Use createParticipant instead
     */
    async createUser(userData: CreateParticipantRequest): Promise<Participant> {
        return this.createParticipant(userData)
    },

    /**
     * Obtiene los datos jerárquicos para los filtros usando la API real
     * @returns Datos jerárquicos organizados
     */
    async getHierarchicalData(): Promise<HierarchicalFilterData> {
        try {
            // Usar el servicio de datos jerárquicos para obtener datos reales de la API
            return await HierarchicalDataService.getHierarchicalData()
        } catch (error) {
            console.error('Error al obtener datos jerárquicos:', error)
            throw new Error('No se pudieron cargar los datos jerárquicos')
        }
    },

    /**
     * Busca participantes con filtros jerárquicos específicos
     * Combina búsqueda por texto libre con filtros estructurales
     *
     * @param searchTerm - Término de búsqueda libre (nombre, email, cargo)
     * @param hierarchicalFilters - Filtros jerárquicos (campo, zona, distrito, iglesia, cargo)
     * @returns Promesa con la lista de participantes filtrados
     */
    async searchParticipantsWithFilters(
        searchTerm?: string,
        hierarchicalFilters?: {
            fieldId?: string | number | null
            zoneId?: string | number | null
            districtId?: string | number | null
            churchId?: string | number | null
            ecclesiasticalRole?: string | null
        }
    ): Promise<Participant[]> {
        try {
            // Construir filtros combinados
            const filters: ParticipantFilters = {}

            // Agregar búsqueda por texto libre si se proporciona
            if (searchTerm && searchTerm.trim()) {
                filters.search = searchTerm.trim()
            }

            // Agregar filtros jerárquicos si se proporcionan
            if (hierarchicalFilters) {
                if (hierarchicalFilters.fieldId) {
                    filters.fieldAssignmentId = hierarchicalFilters.fieldId
                }
                if (hierarchicalFilters.zoneId) {
                    filters.zoneAssignmentId = hierarchicalFilters.zoneId
                }
                if (hierarchicalFilters.districtId) {
                    filters.districtAssignmentId = hierarchicalFilters.districtId
                }
                if (hierarchicalFilters.churchId) {
                    filters.churchAssignmentId = hierarchicalFilters.churchId
                }
                if (hierarchicalFilters.ecclesiasticalRole) {
                    filters.ecclesiasticalRole = hierarchicalFilters.ecclesiasticalRole
                }
            }

            // Usar el método existente getParticipants con los filtros construidos
            return await this.getParticipants(filters)
        } catch (error) {
            console.error('Error al buscar participantes con filtros:', error)
            throw new Error('No se pudieron buscar los participantes')
        }
    },

    /**
     * Obtiene los roles eclesiásticos únicos desde la API
     * @returns Promesa con la lista de roles únicos
     */
    async getEcclesiasticalRoles(): Promise<string[]> {
        try {
            return await HierarchicalDataService.getEcclesiasticalRoles()
        } catch (error) {
            console.error('Error al obtener roles eclesiásticos:', error)
            throw new Error('No se pudieron obtener los roles eclesiásticos')
        }
    }
}

export default ParticipantsService
