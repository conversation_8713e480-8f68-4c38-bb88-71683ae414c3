# Gestión Jerárquica de Participantes - Documentación Técnica

## Descripción General

Este documento describe la implementación completa del sistema de gestión jerárquica de participantes, diseñado para trabajar con la API de Strapi v5 y manejar la estructura organizacional de la iglesia de manera eficiente.

## Arquitectura del Sistema

### Estructura Jerárquica Organizacional

```
Field (Campo/Asociación)
├── Zone (Zona)
    ├── District (Distrito)
        ├── Church (Iglesia)
            └── Participant (Participante)
```

### Componentes Principales

#### 1. Tipos y Interfaces (`src/features/events/types/participant.ts`)

**Interface Principal - Participant**
```typescript
/**
 * Interface principal para participantes con estructura jerárquica dual
 * Maneja tanto objetos completos (GET) como IDs (POST/PUT) para optimizar
 * la comunicación con Strapi v5
 */
export interface Participant {
    // Identificadores únicos
    id: string | number
    documentId?: string
    
    // Información personal básica
    firstName: string
    lastName: string
    email: string
    phone?: string
    ecclesiasticalRole?: string
    
    // Multimedia
    avatar?: Media | string | null
    
    // Cuenta de usuario asociada (opcional)
    userAccount?: UserAccount | null
    
    // Objetos jerárquicos completos (para respuestas GET)
    fieldAssignment?: Field | null
    zoneAssignment?: Zone | null
    districtAssignment?: District | null
    churchAssignment?: Church | null
    
    // IDs de compatibilidad (para filtros y referencias)
    fieldAssignmentId?: number | string | null
    zoneAssignmentId?: number | string | null
    districtAssignmentId?: number | string | null
    churchAssignmentId?: number | string | null
}
```

**Interfaces de Request**
```typescript
/**
 * Interface para crear participantes - solo IDs para relaciones
 * Strapi v5 maneja automáticamente las relaciones cuando se proporcionan IDs
 */
export interface CreateParticipantRequest {
    firstName: string
    lastName: string
    email: string
    phone?: string
    ecclesiasticalRole?: string
    // Solo documentIds para relaciones
    fieldAssignment?: string | null
    zoneAssignment?: string | null
    districtAssignment?: string | null
    churchAssignment?: string | null
    userAccount?: string | null
}
```

#### 2. Servicio Principal (`src/features/events/services/ParticipantsService.ts`)

**Método getParticipants - Populate Estratégico**
```typescript
/**
 * Obtiene participantes con populate específico para optimizar rendimiento
 * Evita el uso de 'populate: *' que puede causar sobrecarga de datos
 */
async getParticipants(filters?: ParticipantFilters): Promise<Participant[]> {
    const params = {
        populate: {
            // Avatar del participante
            avatar: true,
            // Cuenta de usuario con su avatar
            userAccount: { populate: ['avatar'] },
            // Campo/Asociación completo (nivel más alto)
            fieldAssignment: { populate: ['*'] },
            // Zona con su campo padre
            zoneAssignment: { populate: ['field'] },
            // Distrito con su zona padre
            districtAssignment: { populate: ['zone'] },
            // Iglesia con su distrito padre
            churchAssignment: { populate: ['district'] }
        }
    }
    // ... lógica de filtros y request
}
```

**Método createParticipant - Solo IDs para Relaciones**
```typescript
/**
 * Crea participantes enviando solo documentIds para relaciones
 * Strapi v5 maneja automáticamente la creación de relaciones
 */
async createParticipant(participantData: CreateParticipantRequest): Promise<Participant> {
    const participantPayload = {
        firstName: participantData.firstName,
        lastName: participantData.lastName,
        email: participantData.email,
        // Solo documentIds - Strapi crea las relaciones automáticamente
        fieldAssignment: participantData.fieldAssignment || null,
        zoneAssignment: participantData.zoneAssignment || null,
        // ... más relaciones
    }
    
    // Strapi v5 requiere envolver datos en objeto 'data'
    const strapiPayload = { data: participantPayload }
    // ... request a la API
}
```

#### 3. Funciones Utilitarias

**enrichParticipantWithNames - Enriquecimiento de Datos**
```typescript
/**
 * Enriquece participantes con IDs de compatibilidad
 * Extrae documentIds de objetos jerárquicos para usar en filtros locales
 */
export function enrichParticipantWithNames(participant: Participant): Participant {
    return {
        ...participant,
        fieldAssignmentId: participant.fieldAssignment?.documentId || participant.fieldAssignmentId,
        zoneAssignmentId: participant.zoneAssignment?.documentId || participant.zoneAssignmentId,
        districtAssignmentId: participant.districtAssignment?.documentId || participant.districtAssignmentId,
        churchAssignmentId: participant.churchAssignment?.documentId || participant.churchAssignmentId,
    }
}
```

## Estrategias de Implementación

### 1. Populate Estratégico vs Populate Completo

**❌ Problemático - Populate Completo**
```typescript
// Causa sobrecarga de datos y posibles errores
populate: '*'
```

**✅ Recomendado - Populate Específico**
```typescript
// Carga solo las relaciones necesarias
populate: {
    fieldAssignment: { populate: ['*'] },
    zoneAssignment: { populate: ['field'] },
    districtAssignment: { populate: ['zone'] },
    churchAssignment: { populate: ['district'] }
}
```

### 2. Filtros con documentId para Strapi v5

**❌ Problemático - Usar 'id'**
```typescript
// No funciona correctamente en Strapi v5
filters: {
    fieldAssignment: { id: 'field-1' }
}
```

**✅ Recomendado - Usar 'documentId'**
```typescript
// Funciona correctamente en Strapi v5
filters: {
    fieldAssignment: { documentId: 'field-1' }
}
```

### 3. Manejo Dual de Estructuras

**Respuestas GET - Objetos Completos**
```typescript
// Lo que recibimos de Strapi en GET
{
    id: 1,
    firstName: "Juan",
    fieldAssignment: {
        id: 1,
        documentId: "field-1",
        name: "Asociación Central",
        acronym: "ACD"
    }
}
```

**Requests POST/PUT - Solo IDs**
```typescript
// Lo que enviamos a Strapi en POST/PUT
{
    firstName: "Juan",
    fieldAssignment: "field-1" // Solo documentId
}
```

## Beneficios de la Implementación

### 1. Rendimiento Optimizado
- **Populate específico**: Reduce transferencia de datos innecesarios
- **Filtros eficientes**: Uso correcto de documentId para consultas rápidas
- **Cache local**: Enriquecimiento automático para compatibilidad

### 2. Mantenibilidad
- **Código documentado**: Comentarios en español para facilitar comprensión
- **Separación de responsabilidades**: Interfaces claras para diferentes contextos
- **Funciones utilitarias**: Reutilización de lógica común

### 3. Escalabilidad
- **Estructura jerárquica**: Fácil agregar nuevos niveles organizacionales
- **Compatibilidad**: Métodos deprecated para transición gradual
- **Flexibilidad**: Manejo de casos con y sin relaciones jerárquicas

### 4. Robustez
- **Manejo de errores**: Información detallada para debugging
- **Validación**: Verificación de datos antes de envío a API
- **Pruebas unitarias**: Cobertura completa de funcionalidades

## Consideraciones de Implementación

### Migración de Datos Existentes
1. **Identificar participantes sin estructura jerárquica**
2. **Mapear a organizaciones existentes**
3. **Actualizar referencias en eventos**
4. **Validar integridad de datos**

### Compatibilidad hacia Atrás
- Mantener métodos deprecated durante transición
- Enriquecimiento automático para componentes legacy
- Validación gradual de nuevas funcionalidades

### Consideraciones de Seguridad
- Validar permisos para operaciones jerárquicas
- Filtrar datos según nivel de acceso del usuario
- Auditoría de cambios en estructura organizacional

## Pruebas y Validación

### Cobertura de Pruebas Unitarias
- ✅ Obtención con populate jerárquico
- ✅ Filtros con documentId
- ✅ Creación con solo IDs
- ✅ Búsqueda por email
- ✅ Manejo de errores

### Casos de Prueba Críticos
1. **Participante con todas las relaciones jerárquicas**
2. **Participante sin relaciones (casos básicos)**
3. **Filtros combinados (campo + zona + distrito)**
4. **Creación con relaciones parciales**
5. **Manejo de errores de API**

## Próximos Pasos

### Funcionalidades Pendientes
1. **Endpoints jerárquicos**: Servicios para obtener fields, zones, districts, churches
2. **Filtros en cascada**: UX mejorada para selección jerárquica
3. **Reportes jerárquicos**: Estadísticas por nivel organizacional
4. **Sincronización**: Actualización automática de cambios organizacionales

### Optimizaciones Futuras
1. **Cache inteligente**: Cache por nivel jerárquico
2. **Paginación avanzada**: Paginación por grupos jerárquicos
3. **Búsqueda avanzada**: Búsqueda por múltiples criterios jerárquicos
4. **Exportación**: Reportes con estructura jerárquica
