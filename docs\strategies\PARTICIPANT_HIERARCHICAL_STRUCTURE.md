# Estrategia: Estructura Jerárquica de Participantes con Strapi v5

## Problema

El sistema original de participantes no estaba alineado con la estructura real de la API de Strapi v5, lo que causaba:

1. **Inconsistencia de datos**: Los participantes se manejaban como usuarios simples sin estructura jerárquica
2. **Populate incorrecto**: Se usaba `populate: '*'` que cargaba datos innecesarios y podía fallar
3. **Filtros ineficientes**: Los filtros usaban `id` en lugar de `documentId` requerido por Strapi v5
4. **Estructura dual no manejada**: Strapi devuelve objetos completos en GET pero requiere solo IDs en POST/PUT
5. **Separación conceptual**: No se distinguía entre "usuarios" (cuentas del sistema) y "participantes" (asistentes a eventos)

## Solución

### Estructura Jerárquica Implementada

```
Field (Campo/Asociación)
├── Zone (Zona)
    ├── District (Distrito)
        ├── Church (Iglesia)
            └── Participant (Participante)
```

### Manejo Dual de Datos

#### 1. Respuestas GET - Objetos Completos
```typescript
interface Participant {
  id: number | string
  documentId: string
  firstName: string
  lastName: string
  email: string
  // Objetos jerárquicos completos
  fieldAssignment?: {
    id: number
    documentId: string
    name: string
    acronym: string
    type: string
  }
  zoneAssignment?: {
    id: number
    documentId: string
    name: string
    field: Field // Relación poblada
  }
  // ... más relaciones
}
```

#### 2. Requests POST/PUT - Solo IDs
```typescript
interface CreateParticipantRequest {
  firstName: string
  lastName: string
  email: string
  // Solo documentIds para relaciones
  fieldAssignment?: string | null
  zoneAssignment?: string | null
  districtAssignment?: string | null
  churchAssignment?: string | null
}
```

### Populate Estratégico

En lugar de `populate: '*'`, se implementó populate específico:

```typescript
populate: {
  avatar: true,
  userAccount: {
    populate: ['avatar']
  },
  fieldAssignment: {
    populate: ['*'] // Campo completo
  },
  zoneAssignment: {
    populate: ['field'] // Zona con su campo
  },
  districtAssignment: {
    populate: ['zone'] // Distrito con su zona
  },
  churchAssignment: {
    populate: ['district'] // Iglesia con su distrito
  }
}
```

## Alternativas Consideradas

### 1. Mantener Estructura Plana
- **Rechazada**: No refleja la realidad organizacional de la iglesia
- **Problema**: Dificulta filtros jerárquicos y reportes

### 2. Usar Solo IDs en Todas las Operaciones
- **Rechazada**: Requiere múltiples requests para obtener nombres y detalles
- **Problema**: Impacto negativo en rendimiento y UX

### 3. Populate Completo (`populate: '*'`)
- **Rechazada**: Carga datos innecesarios y puede causar errores
- **Problema**: Transferencia excesiva de datos y posibles loops de relaciones

## Limitaciones

1. **Complejidad de Filtros**: Los filtros jerárquicos requieren lógica más compleja
2. **Dependencia de Strapi v5**: La implementación está específicamente diseñada para Strapi v5
3. **Migración de Datos**: Requiere migración de datos existentes a la nueva estructura
4. **Curva de Aprendizaje**: Los desarrolladores deben entender la estructura dual

## Validación

### Pruebas Unitarias Implementadas

1. **✅ Obtención con Populate**: Verifica que se carguen correctamente las relaciones jerárquicas
2. **✅ Filtros con documentId**: Confirma que los filtros usen documentId para Strapi v5
3. **✅ Creación con IDs**: Valida que los requests POST envíen solo IDs
4. **✅ Búsqueda por Email**: Verifica populate completo en búsquedas
5. **✅ Manejo de Errores**: Confirma manejo robusto de casos de error

### Resultados de Pruebas
```bash
✓ ParticipantsService > getParticipants > debe obtener participantes con populate de relaciones jerárquicas
✓ ParticipantsService > getParticipants > debe aplicar filtros correctamente usando documentId  
✓ ParticipantsService > createParticipant > debe crear un participante con relaciones jerárquicas usando IDs
✓ ParticipantsService > findParticipantByEmail > debe buscar un participante por email con populate completo
✓ ParticipantsService > findParticipantByEmail > debe retornar null si no encuentra el participante

Test Files  1 passed (1)
Tests  5 passed (5)
```

## Ejemplos de Implementación

### Servicio de Participantes
```typescript
/**
 * Obtiene todos los participantes del sistema con populate de relaciones jerárquicas
 * @param filters - Filtros opcionales para la búsqueda
 * @returns Promesa con la lista de participantes con objetos jerárquicos completos
 */
async getParticipants(filters?: ParticipantFilters): Promise<Participant[]> {
  // Implementación con populate estratégico...
}
```

### Función de Enriquecimiento
```typescript
/**
 * Enriquece un participante con nombres de asignaciones jerárquicas para mostrar en UI
 * Extrae los documentIds de los objetos jerárquicos para compatibilidad con filtros
 */
export function enrichParticipantWithNames(participant: Participant): Participant {
  return {
    ...participant,
    fieldAssignmentId: participant.fieldAssignment?.documentId || participant.fieldAssignmentId,
    zoneAssignmentId: participant.zoneAssignment?.documentId || participant.zoneAssignmentId,
    districtAssignmentId: participant.districtAssignment?.documentId || participant.districtAssignmentId,
    churchAssignmentId: participant.churchAssignment?.documentId || participant.churchAssignmentId,
  }
}
```

## Beneficios Obtenidos

1. **Alineación con API Real**: Estructura coincide exactamente con Strapi v5
2. **Mejor Rendimiento**: Populate específico reduce transferencia de datos
3. **Filtros Eficientes**: Uso correcto de documentId para filtros
4. **Separación Clara**: Distinción entre usuarios y participantes
5. **Mantenibilidad**: Código más organizado y documentado
6. **Escalabilidad**: Fácil agregar nuevos niveles jerárquicos

## Consideraciones de Implementación

### Compatibilidad hacia Atrás
- Se mantienen métodos deprecated para transición gradual
- Los componentes existentes siguen funcionando durante la migración

### Migración Gradual
1. Implementar nuevos servicios con estructura jerárquica
2. Actualizar componentes uno por uno
3. Deprecar métodos antiguos gradualmente
4. Remover código legacy después de validación completa
