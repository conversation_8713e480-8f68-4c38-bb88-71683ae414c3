/**
 * Vista para el registro de asistencia a un evento
 */
import React from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import Card from '@/shared/components/ui/Card'
import Button from '@/shared/components/ui/Button'
import Checkbox from '@/shared/components/ui/Checkbox'
import Avatar from '@/shared/components/ui/Avatar'
import Input from '@/shared/components/ui/Input'
import Radio from '@/shared/components/ui/Radio'
import { FormItem } from '@/shared/components/ui/Form'
import Loading from '@/shared/components/shared/Loading'
import {
    HiArrowLeft,
    HiSave,
    HiCheck,
    HiCalendar,
    HiClock,
    HiLocationMarker,
    HiDownload,
    HiUserAdd,
} from 'react-icons/hi'
import type { Event, Participant, AttendanceRecord, Department } from '../../types'
import toast from '@/shared/components/ui/toast/toast'
import Notification from '@/shared/components/ui/Notification'
import StatusBadge from '../../components/StatusBadge'
import exportEventParticipants from '../../utils/exportEventParticipants'
import {
    canCompleteEvent,
    determineEventStatus,
} from '../../utils/eventStatusUtils'
import { formatDate } from '../../utils/dateUtils'
import { formatTime } from '../../utils/timeUtils'
import { getDepartmentNameById } from '../../utils/departmentUtils'
import { sanitizeEvent } from '../../utils/eventDataUtils'
import EventsService from '../../services/EventsService'
import useAuth from '@/features/auth/hooks/useAuth'
import { useToast } from '@/shared/hooks/useToast'
import * as Yup from 'yup'
import useAttendance from '../../hooks/useAttendance'

/**
 * Interfaz para el estado de asistencia de un participante
 */
interface ParticipantAttendanceState {
    status?: 'asistio' | 'no_asistio'
    reason?: string
    isPartial?: boolean
    partialComment?: string
    timeStayed?: string
}

// Esquema de validación para cada participante al finalizar
const participantFinalizeSchema = Yup.object().shape({
    status: Yup.string()
        .oneOf(
            ['asistio', 'no_asistio'],
            'El estado debe ser "Asistió" o "No Asistió".',
        )
        .required('Debe seleccionar el estado de asistencia.'),
    isPartial: Yup.boolean().optional(), // Usado para la lógica condicional de timeStayed
    timeStayed: Yup.string().when(['status', 'isPartial'], {
        is: (status?: string, isPartial?: boolean) =>
            status === 'asistio' && isPartial,
        then: (schema) =>
            schema.required(
                'Debe ingresar el tiempo de permanencia para asistencia parcial.',
            ),
        otherwise: (schema) => schema.optional(),
    }),
})

/**
 * Componente para el registro de asistencia a un evento
 */
const AttendanceView = () => {
    // Obtener el ID del evento de los parámetros de la URL
    const { id: eventId, sessionId } = useParams<{
        id: string
        sessionId: string
    }>()

    const {
        event,
        participantStates,
        loading,
        saving,
        error,
        navigateBack,
        getBackButtonText,
        handleAttendanceStatusChange,
        handleReasonChange,
        handlePartialAttendanceChange,
        handlePartialCommentChange,
        handleTimeStayedChange,
        handleSaveProgress,
        handleSaveAndFinalize,
    } = useAttendance(eventId, sessionId)

    const navigate = useNavigate()
    const [departments, setDepartments] = useState<Department[]>([])

    const handleBack = () => {
        navigateBack()
    }

    // Cargar departamentos al montar el componente
    useEffect(() => {
        const loadDepartments = async () => {
            try {
                const departmentsData = await EventsService.getDepartments()
                setDepartments(departmentsData)
            } catch (error) {
                console.error('Error al cargar departamentos:', error)
            }
        }
        loadDepartments()
    }, [])



    if (loading) {
        return (
            <div className="flex justify-center items-center h-full p-8">
                <Loading loading={true} />
                <p className="ml-2">Cargando datos del evento...</p>
            </div>
        )
    }

    if (error && !event) {
        return (
            <div className="flex flex-col items-center justify-center h-full p-8">
                <p className="text-lg mb-4 text-red-500">{error}</p>
                <Button
                    variant="solid"
                    onClick={() => navigate('/events/list')}
                >
                    Volver a la lista
                </Button>
            </div>
        )
    }

    if (!event) {
        return (
            <div className="flex flex-col items-center justify-center h-full p-8">
                <p className="text-lg mb-4">
                    No se encontró el evento solicitado
                </p>
                <Button
                    variant="solid"
                    onClick={() => navigate('/events/list')}
                >
                    Volver a la lista
                </Button>
            </div>
        )
    }

    const isCompletedEvent = event.status === 'completada'

    return (
        <div className="container mx-auto p-4">
            {/* Mostrar mensaje de error si existe */}
            {error && (
                <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
                    {error}
                </div>
            )}

            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                    <Button
                        className="mr-4"
                        icon={<HiArrowLeft />}
                        onClick={handleBack}
                        variant="plain"
                        disabled={saving}
                    >
                        {getBackButtonText()}
                    </Button>
                    <h1 className="text-2xl font-bold">
                        Registro de Asistencia
                    </h1>
                </div>
                <div className="flex space-x-2">
                    <Button
                        size="sm"
                        variant="plain"
                        icon={<HiDownload />}
                        onClick={() => event && exportEventParticipants(event)}
                        disabled={saving}
                        title="Descargar participantes"
                    />
                    <Button
                        size="sm"
                        variant="plain"
                        icon={<HiUserAdd />}
                        onClick={() => {
                            // Navegar al registro de participantes preservando el contexto de navegación
                            navigate(`/events/${event.id}/register-participant`, {
                                state: {
                                    navigationContext: {
                                        origin: 'attendance',
                                        returnPath: `/events/${event.id}/sessions/${sessionId}/asistencia`,
                                        eventId: event.id,
                                        sessionId: sessionId
                                    }
                                }
                            })
                        }}
                        disabled={saving}
                        title="Registrar nuevo participante"
                    />
                </div>
            </div>

            {/* Información del Evento */}
            <Card className="mb-6">
                <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                        <h5>{event.title}</h5>
                        <StatusBadge status={event.status} />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="flex items-center">
                            <HiCalendar className="text-lg mr-2" />
                            <span>{formatDate(event.date)}</span>
                        </div>
                        <div className="flex items-center">
                            <HiClock className="text-lg mr-2" />
                            <span>
                                {formatTime(event.startTime)} - {formatTime(event.endTime)}
                            </span>
                        </div>
                        <div className="flex items-center">
                            <HiLocationMarker className="text-lg mr-2" />
                            <span>{event.location}</span>
                        </div>
                        {/* Mostrar el departamento que invita si está disponible */}
                        {event.invitingDepartment && (
                            <div className="flex items-center mb-2">
                                <span className="font-semibold mr-2">
                                    Departamento que invita:
                                </span>
                                <span>{getDepartmentNameById(event.invitingDepartment, departments)}</span>
                            </div>
                        )}
                    </div>
                    {/* Departamento que invita */}
                    {event.invitingDepartment && (
                        <p className="flex items-center text-gray-600 dark:text-gray-400 mt-2 mb-1">
                            <span className="font-semibold mr-2">Invita:</span>
                            <span>{getDepartmentNameById(event.invitingDepartment, departments)}</span>
                        </p>
                    )}
                </div>
            </Card>

            {/* Lista de Convocados */}
            <Card className="mb-6">
                <div className="p-6">
                    <h5 className="mb-4">Lista de Convocados</h5>

                    <div className="space-y-4">
                        {(
                            Array.isArray(event.participantsInvited)
                                ? event.participantsInvited.filter(
                                      (p) => p && p.id !== undefined,
                                  )
                                : []
                        ).map((participant) => (
                            <div
                                key={participant.id}
                                className="flex flex-col md:flex-row md:items-start p-4 border rounded-lg mb-4 shadow-sm"
                            >
                                {/* Columna de Información del Participante */}
                                <div className="flex items-center mb-4 md:mb-0 md:w-2/5 lg:w-1/3 md:pr-6">
                                    <Avatar
                                        src={
                                            typeof participant.avatar ===
                                            'string'
                                                ? participant.avatar
                                                : participant.avatar?.url
                                        }
                                        size={48}
                                        className="mr-3 flex-shrink-0"
                                    />
                                    <div className="overflow-hidden">
                                        <div className="font-semibold text-base truncate">
                                            {participant.firstName}{' '}
                                            {participant.lastName}
                                        </div>
                                        <div className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                            {participant.ecclesiasticalRole ||
                                                participant.email}
                                        </div>
                                    </div>
                                </div>

                                {/* Columna de Controles de Asistencia */}
                                <div className="flex flex-col space-y-3 md:w-3/5 lg:w-2/3 md:pl-6 border-t md:border-t-0 md:border-l border-gray-200 dark:border-gray-600 pt-4 md:pt-0">
                                    <div className="mb-2">
                                        <Radio.Group
                                            value={
                                                participantStates[
                                                    participant.id.toString()
                                                ]?.status
                                            }
                                            onChange={(value) =>
                                                handleAttendanceStatusChange(
                                                    participant.id.toString(),
                                                    value,
                                                )
                                            }
                                            disabled={isCompletedEvent}
                                        >
                                            <Radio value="asistio">
                                                Asistió
                                            </Radio>
                                            <Radio value="no_asistio">
                                                No Asistió
                                            </Radio>
                                        </Radio.Group>
                                    </div>

                                    {/* Renderizado condicional para No Asistió */}
                                    {participantStates[
                                        participant.id.toString()
                                    ]?.status === 'no_asistio' && (
                                        <FormItem
                                            label="Razón de Ausencia"
                                            className="mb-3"
                                        >
                                            <Input
                                                textArea
                                                placeholder="Ingrese la razón de ausencia"
                                                value={
                                                    participantStates[
                                                        participant.id.toString()
                                                    ]?.reason || ''
                                                }
                                                onChange={(e) =>
                                                    handleReasonChange(
                                                        participant.id.toString(),
                                                        e.target.value,
                                                    )
                                                }
                                                disabled={isCompletedEvent}
                                            />
                                        </FormItem>
                                    )}

                                    {/* Renderizado condicional para Asistió */}
                                    {participantStates[
                                        participant.id.toString()
                                    ]?.status === 'asistio' && (
                                        <div className="space-y-3">
                                            <Checkbox
                                                checked={
                                                    participantStates[
                                                        participant.id.toString()
                                                    ]?.isPartial || false
                                                }
                                                onChange={(checked) =>
                                                    handlePartialAttendanceChange(
                                                        participant.id.toString(),
                                                        checked,
                                                    )
                                                }
                                                disabled={isCompletedEvent}
                                                className="mb-2"
                                            >
                                                Asistencia Parcial
                                            </Checkbox>

                                            {/* Campos adicionales para asistencia parcial */}
                                            {participantStates[
                                                participant.id.toString()
                                            ]?.isPartial && (
                                                <div className="space-y-3 pl-6">
                                                    <FormItem
                                                        label="Comentario de Asistencia Parcial"
                                                        className="mb-3"
                                                    >
                                                        <Input
                                                            placeholder="Ingrese un comentario sobre la asistencia parcial"
                                                            value={
                                                                participantStates[
                                                                    participant.id.toString()
                                                                ]
                                                                    ?.partialComment ||
                                                                ''
                                                            }
                                                            onChange={(e) =>
                                                                handlePartialCommentChange(
                                                                    participant.id.toString(),
                                                                    e.target
                                                                        .value,
                                                                )
                                                            }
                                                            disabled={
                                                                isCompletedEvent
                                                            }
                                                        />
                                                    </FormItem>
                                                    <FormItem
                                                        label="Tiempo Aproximado en Evento"
                                                        className="mb-3"
                                                    >
                                                        <Input
                                                            placeholder="Ej: 30 minutos, 1 hora"
                                                            value={
                                                                participantStates[
                                                                    participant.id.toString()
                                                                ]?.timeStayed ||
                                                                ''
                                                            }
                                                            onChange={(e) =>
                                                                handleTimeStayedChange(
                                                                    participant.id.toString(),
                                                                    e.target
                                                                        .value,
                                                                )
                                                            }
                                                            disabled={
                                                                isCompletedEvent
                                                            }
                                                        />
                                                    </FormItem>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </Card>

            {/* Botones de Acción */}
            {/* Variable para habilitar/deshabilitar el botón de finalizar */}
            {event &&
                (() => {
                    const allParticipantsMarked =
                        event.participantsInvited.every(
                            (p) =>
                                participantStates[p.id?.toString()]?.status ===
                                    'asistio' ||
                                participantStates[p.id?.toString()]?.status ===
                                    'no_asistio',
                        )
                    return (
                        !isCompletedEvent && (
                            <div className="flex justify-end space-x-2">
                                <Button
                                    variant="solid"
                                    color="blue-500"
                                    icon={<HiSave />}
                                    onClick={handleSaveProgress}
                                    disabled={saving}
                                >
                                    Guardar Progreso Asistencia
                                </Button>
                                <Button
                                    variant="solid"
                                    color="green-500"
                                    icon={<HiCheck />}
                                    onClick={handleSaveAndFinalize}
                                    disabled={!allParticipantsMarked || saving}
                                    title={
                                        !allParticipantsMarked
                                            ? 'Debe marcar asistencia de todos los participantes para finalizar.'
                                            : ''
                                    }
                                >
                                    Finalizar y Guardar Asistencia
                                </Button>
                            </div>
                        )
                    )
                })()}
        </div>
    )
}

export default AttendanceView
