import type { Event, Participant } from '../types'

/**
 * Asigna un ID único a los participantes que no lo tengan definido.
 */
export const ensureParticipantIds = (
    participants: Participant[],
): Participant[] =>
    participants.map((p) => {
        if (!p.id) {
            return {
                ...p,
                id: `${Date.now()}-${Math.random().toString(36).slice(2)}`,
            }
        }
        return p
    })

/**
 * Normaliza las fechas y participantes de un evento para evitar errores
 * de renderizado en los componentes de asistencia.
 */
export const sanitizeEvent = (event: Event): Event => {
    const normalizeDate = (d: string | Date | undefined) => {
        if (!d) return d as any
        return typeof d === 'string'
            ? d
            : new Date(d).toISOString().split('T')[0]
    }

    const normalizeTime = (t: string | Date | undefined) => {
        if (!t) return t as any
        if (typeof t === 'string') {
            const match = t.match(/(\d{2}:\d{2})/)
            if (match) {
                return match[1]
            }
            const parsed = new Date(t)
            if (!isNaN(parsed.getTime())) {
                return parsed.toISOString().split('T')[1].substring(0, 5)
            }
            return t
        }
        const hours = t.getHours().toString().padStart(2, '0')
        const minutes = t.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
    }

    return {
        ...event,
        date: normalizeDate(event.date) as string,
        endDate: normalizeDate(event.endDate) as string | undefined,
        startTime: normalizeTime(event.startTime) as string,
        endTime: normalizeTime(event.endTime) as string | undefined,
        sessions: event.sessions.map((s) => ({
            ...s,
            date: normalizeDate(s.date) as string,
        })),
        participantsInvited: ensureParticipantIds(event.participantsInvited),
    }
}

