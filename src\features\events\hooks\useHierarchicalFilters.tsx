import { useState, useEffect, useCallback } from 'react'
import HierarchicalDataService from '../services/HierarchicalDataService'
import type { Field, Zone, District, Church } from '../services/HierarchicalDataService'

/**
 * Interfaz para las opciones de filtros jerárquicos
 */
export interface HierarchicalFilterOptions {
    value: string | number | null
    label: string
}

/**
 * Estado de los filtros jerárquicos
 */
export interface HierarchicalFiltersState {
    // Datos disponibles
    fields: Field[]
    zones: Zone[]
    districts: District[]
    churches: Church[]
    
    // Valores seleccionados
    selectedField: string | number | null
    selectedZone: string | number | null
    selectedDistrict: string | number | null
    selectedChurch: string | number | null
    
    // Estados de carga
    isLoadingFields: boolean
    isLoadingZones: boolean
    isLoadingDistricts: boolean
    isLoadingChurches: boolean
    
    // Opciones formateadas para los Select components
    fieldOptions: HierarchicalFilterOptions[]
    zoneOptions: HierarchicalFilterOptions[]
    districtOptions: HierarchicalFilterOptions[]
    churchOptions: HierarchicalFilterOptions[]
}

/**
 * Acciones disponibles para los filtros jerárquicos
 */
export interface HierarchicalFiltersActions {
    setSelectedField: (fieldId: string | number | null) => void
    setSelectedZone: (zoneId: string | number | null) => void
    setSelectedDistrict: (districtId: string | number | null) => void
    setSelectedChurch: (churchId: string | number | null) => void
    resetFilters: () => void
    loadInitialData: () => Promise<void>
}

/**
 * Hook personalizado para manejar filtros jerárquicos en cascada
 * 
 * Este hook gestiona la lógica de filtros jerárquicos donde la selección de un nivel
 * superior (Campo) determina las opciones disponibles en los niveles inferiores
 * (Zona → Distrito → Iglesia).
 * 
 * Características:
 * - Carga automática de datos desde la API
 * - Filtrado en cascada automático
 * - Reseteo automático de niveles inferiores cuando cambia un nivel superior
 * - Estados de carga independientes para cada nivel
 * - Manejo de errores con logging detallado
 * 
 * @returns Estado y acciones para manejar filtros jerárquicos
 */
export const useHierarchicalFilters = (): HierarchicalFiltersState & HierarchicalFiltersActions => {
    // Estados para los datos jerárquicos
    const [fields, setFields] = useState<Field[]>([])
    const [zones, setZones] = useState<Zone[]>([])
    const [districts, setDistricts] = useState<District[]>([])
    const [churches, setChurches] = useState<Church[]>([])
    
    // Estados para las selecciones actuales
    const [selectedField, setSelectedFieldState] = useState<string | number | null>(null)
    const [selectedZone, setSelectedZoneState] = useState<string | number | null>(null)
    const [selectedDistrict, setSelectedDistrictState] = useState<string | number | null>(null)
    const [selectedChurch, setSelectedChurchState] = useState<string | number | null>(null)
    
    // Estados de carga
    const [isLoadingFields, setIsLoadingFields] = useState(false)
    const [isLoadingZones, setIsLoadingZones] = useState(false)
    const [isLoadingDistricts, setIsLoadingDistricts] = useState(false)
    const [isLoadingChurches, setIsLoadingChurches] = useState(false)

    /**
     * Carga los datos iniciales (campos) desde la API
     */
    const loadInitialData = useCallback(async () => {
        setIsLoadingFields(true)
        try {
            const fieldsData = await HierarchicalDataService.getFields()
            setFields(fieldsData)
        } catch (error) {
            console.error('Error al cargar campos:', error)
            setFields([])
        } finally {
            setIsLoadingFields(false)
        }
    }, [])

    /**
     * Carga las zonas filtradas por el campo seleccionado
     */
    const loadZonesByField = useCallback(async (fieldId: string | number) => {
        setIsLoadingZones(true)
        try {
            const zonesData = await HierarchicalDataService.getZonesByField(fieldId)
            setZones(zonesData)
        } catch (error) {
            console.error('Error al cargar zonas:', error)
            setZones([])
        } finally {
            setIsLoadingZones(false)
        }
    }, [])

    /**
     * Carga los distritos filtrados por la zona seleccionada
     */
    const loadDistrictsByZone = useCallback(async (zoneId: string | number) => {
        setIsLoadingDistricts(true)
        try {
            const districtsData = await HierarchicalDataService.getDistrictsByZone(zoneId)
            setDistricts(districtsData)
        } catch (error) {
            console.error('Error al cargar distritos:', error)
            setDistricts([])
        } finally {
            setIsLoadingDistricts(false)
        }
    }, [])

    /**
     * Carga las iglesias filtradas por el distrito seleccionado
     */
    const loadChurchesByDistrict = useCallback(async (districtId: string | number) => {
        setIsLoadingChurches(true)
        try {
            const churchesData = await HierarchicalDataService.getChurchesByDistrict(districtId)
            setChurches(churchesData)
        } catch (error) {
            console.error('Error al cargar iglesias:', error)
            setChurches([])
        } finally {
            setIsLoadingChurches(false)
        }
    }, [])

    /**
     * Maneja la selección de campo y carga las zonas correspondientes
     */
    const setSelectedField = useCallback((fieldId: string | number | null) => {
        setSelectedFieldState(fieldId)
        
        // Resetear niveles inferiores cuando cambia el campo
        setSelectedZoneState(null)
        setSelectedDistrictState(null)
        setSelectedChurchState(null)
        setZones([])
        setDistricts([])
        setChurches([])
        
        // Cargar zonas si se seleccionó un campo
        if (fieldId) {
            loadZonesByField(fieldId)
        }
    }, [loadZonesByField])

    /**
     * Maneja la selección de zona y carga los distritos correspondientes
     */
    const setSelectedZone = useCallback((zoneId: string | number | null) => {
        setSelectedZoneState(zoneId)
        
        // Resetear niveles inferiores cuando cambia la zona
        setSelectedDistrictState(null)
        setSelectedChurchState(null)
        setDistricts([])
        setChurches([])
        
        // Cargar distritos si se seleccionó una zona
        if (zoneId) {
            loadDistrictsByZone(zoneId)
        }
    }, [loadDistrictsByZone])

    /**
     * Maneja la selección de distrito y carga las iglesias correspondientes
     */
    const setSelectedDistrict = useCallback((districtId: string | number | null) => {
        setSelectedDistrictState(districtId)
        
        // Resetear nivel inferior cuando cambia el distrito
        setSelectedChurchState(null)
        setChurches([])
        
        // Cargar iglesias si se seleccionó un distrito
        if (districtId) {
            loadChurchesByDistrict(districtId)
        }
    }, [loadChurchesByDistrict])

    /**
     * Maneja la selección de iglesia
     */
    const setSelectedChurch = useCallback((churchId: string | number | null) => {
        setSelectedChurchState(churchId)
    }, [])

    /**
     * Resetea todos los filtros a su estado inicial
     */
    const resetFilters = useCallback(() => {
        setSelectedFieldState(null)
        setSelectedZoneState(null)
        setSelectedDistrictState(null)
        setSelectedChurchState(null)
        setZones([])
        setDistricts([])
        setChurches([])
    }, [])

    // Cargar datos iniciales al montar el hook
    useEffect(() => {
        loadInitialData()
    }, [loadInitialData])

    // Formatear opciones para los Select components
    const fieldOptions: HierarchicalFilterOptions[] = [
        { value: null, label: 'Todos los Campos' },
        ...fields.map(field => ({
            value: field.id,
            label: field.name
        }))
    ]

    const zoneOptions: HierarchicalFilterOptions[] = [
        { value: null, label: 'Todas las Zonas' },
        ...zones.map(zone => ({
            value: zone.id,
            label: zone.name
        }))
    ]

    const districtOptions: HierarchicalFilterOptions[] = [
        { value: null, label: 'Todos los Distritos' },
        ...districts.map(district => ({
            value: district.id,
            label: district.name
        }))
    ]

    const churchOptions: HierarchicalFilterOptions[] = [
        { value: null, label: 'Todas las Iglesias' },
        ...churches.map(church => ({
            value: church.id,
            label: church.name
        }))
    ]

    return {
        // Datos
        fields,
        zones,
        districts,
        churches,
        
        // Selecciones
        selectedField,
        selectedZone,
        selectedDistrict,
        selectedChurch,
        
        // Estados de carga
        isLoadingFields,
        isLoadingZones,
        isLoadingDistricts,
        isLoadingChurches,
        
        // Opciones formateadas
        fieldOptions,
        zoneOptions,
        districtOptions,
        churchOptions,
        
        // Acciones
        setSelectedField,
        setSelectedZone,
        setSelectedDistrict,
        setSelectedChurch,
        resetFilters,
        loadInitialData
    }
}

export default useHierarchicalFilters
