import { useState, useMemo } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import Card from '@/shared/components/ui/Card'
import Input from '@/shared/components/ui/Input'
import Button from '@/shared/components/ui/Button'
import Avatar from '@/shared/components/ui/Avatar'
import Notification from '@/shared/components/ui/Notification'
import toast from '@/shared/components/ui/toast'
import Dialog from '@/shared/components/ui/Dialog'
import Loading from '@/shared/components/shared/Loading'
import DataTable, { ColumnDef } from '@/shared/components/shared/DataTable'
import type { Event, Participant } from '../../types'
import EventsService from '../../services/EventsService'
import useAttendance from '../../hooks/useAttendance'
import {
    HiSearch,
    HiArrowLeft,
    HiCheckCircle,
    HiCheck,
    HiX,
    HiExclamation,
    HiDownload,
    HiUserAdd,
} from 'react-icons/hi'
import exportEventParticipants from '../../utils/exportEventParticipants'
import { determineEventStatus } from '../../utils/eventStatusUtils'

const FastAttendanceView = () => {
    const { id, sessionId } = useParams<{ id: string; sessionId: string }>()
    const navigate = useNavigate()

    const {
        event,
        loading,
        navigateBack,
        getBackButtonText,
        loadEvent,
    } = useAttendance(id, sessionId)

    const participants = event?.participantsInvited || []
    const currentSession = event?.sessions.find((s) => s.id.toString() === sessionId)

    const [searchTerm, setSearchTerm] = useState('')
    const [showConfirmDialog, setShowConfirmDialog] = useState(false)
    const filteredParticipants = useMemo(() => {
        const term = searchTerm.toLowerCase()
        return participants.filter((p) => {
            if (!term) return true
            return (
                p.firstName?.toLowerCase().includes(term) ||
                p.lastName?.toLowerCase().includes(term) ||
                p.email.toLowerCase().includes(term)
            )
        })
    }, [participants, searchTerm])

    const hasAttended = (participant: Participant) =>
        currentSession?.attendanceRecords?.some(
            (r) =>
                r.person.id.toString() === participant.id.toString() &&
                r.attended,
        )

    // Función para marcar asistencia
    const handleMark = async (participant: Participant) => {
        if (!event || !id || !sessionId) return
        try {
            if (!currentSession) return
            await EventsService.recordAttendance(
                id,
                sessionId,
                participant.id,
                true,
            )
            toast.push(
                <Notification title="Asistencia" type="success">
                    Asistencia registrada para {participant.firstName}{' '}
                    {participant.lastName}
                </Notification>,
            )

            // Actualizar datos localmente
            await loadEvent()
        } catch (error) {
            console.error('Error al registrar asistencia:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    No se pudo registrar la asistencia
                </Notification>,
            )
        }
    }

    // Función para revertir asistencia (marcar como no asistió)
    const handleUnmark = async (participant: Participant) => {
        if (!event || !id || !sessionId) return
        try {
            if (!currentSession) return
            await EventsService.recordAttendance(
                id,
                sessionId,
                participant.id,
                false,
            )
            toast.push(
                <Notification title="Asistencia" type="warning">
                    Asistencia revertida para {participant.firstName}{' '}
                    {participant.lastName}
                </Notification>,
            )

            // Actualizar datos localmente
            await loadEvent()
        } catch (error) {
            console.error('Error al revertir asistencia:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    No se pudo revertir la asistencia
                </Notification>,
            )
        }
    }

    // Función para finalizar el registro y cerrar el evento
    const handleFinishRegistration = () => {
        setShowConfirmDialog(true)
    }

    const confirmFinishRegistration = async () => {
        try {
            setShowConfirmDialog(false)

            if (!event || !currentSession) {
                throw new Error('Evento o sesión no encontrada')
            }

            // Si es una sesión de kiosco, usar el método de completado automático
            if (currentSession.attendanceMode === 'kiosk') {
                await EventsService.completeKioskSession(id!, sessionId!)

                // Obtener el evento actualizado para determinar el estado final
                const updatedEvent = await EventsService.getEventById(id!)
                const finalEventStatus = determineEventStatus(updatedEvent!)

                // Actualizar el estado del evento si es necesario
                if (finalEventStatus !== updatedEvent!.status) {
                    await EventsService.updateEventStatus(id!, finalEventStatus)
                }

                toast.push(
                    <Notification title="Sesión finalizada" type="success">
                        {finalEventStatus === 'completada'
                            ? 'La sesión ha sido completada y el evento ha sido marcado como completado. Los participantes sin registro fueron marcados como ausentes automáticamente.'
                            : 'La sesión ha sido completada. Los participantes sin registro fueron marcados como ausentes automáticamente.'}
                    </Notification>,
                )
            } else {
                // Para sesiones normales, solo actualizar el estado de la sesión
                await EventsService.updateSessionStatus(
                    id!,
                    sessionId!,
                    'completada',
                )

                // Obtener el evento actualizado para determinar el estado final
                const updatedEvent = await EventsService.getEventById(id!)
                const finalEventStatus = determineEventStatus(updatedEvent!)

                // Actualizar el estado del evento si es necesario
                if (finalEventStatus !== updatedEvent!.status) {
                    await EventsService.updateEventStatus(id!, finalEventStatus)
                }

                toast.push(
                    <Notification title="Sesión finalizada" type="success">
                        {finalEventStatus === 'completada'
                            ? 'La sesión ha sido completada y el evento ha sido marcado como completado.'
                            : 'La sesión ha sido completada. El evento se completará cuando todas las sesiones estén finalizadas.'}
                    </Notification>,
                )
            }

            // Navegar de vuelta según el contexto de navegación
            navigateBack()
        } catch (error) {
            console.error('Error al finalizar el registro:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    No se pudo finalizar el registro de asistencia.
                </Notification>,
            )
        } finally {
            await loadEvent()
        }
    }

    const cancelFinishRegistration = () => {
        setShowConfirmDialog(false)
    }

    if (loading && !event) {
        return (
            <div className="flex justify-center p-8">
                <Loading loading={true} />
            </div>
        )
    }

    if (!event) return <div className="p-4">No se encontró el evento</div>

    const columns: ColumnDef<Participant>[] = [
        {
            header: 'Avatar',
            accessorKey: 'avatar',
            cell: (props) => (
                <Avatar
                    src={
                        typeof props.row.original.avatar === 'string'
                            ? props.row.original.avatar
                            : props.row.original.avatar?.url
                    }
                    size={35}
                />
            ),
        },
        {
            header: 'Nombre',
            accessorKey: 'firstName',
            cell: (props) => (
                <span>
                    {props.row.original.firstName} {props.row.original.lastName}
                </span>
            ),
        },
        {
            header: 'Cargo',
            accessorKey: 'ecclesiasticalRole',
            cell: (props) => (
                <span>{props.row.original.ecclesiasticalRole}</span>
            ),
        },
        {
            header: 'Estado',
            id: 'status',
            cell: (props) => {
                const attended = hasAttended(props.row.original)
                return attended ? (
                    <span className="text-emerald-600 flex items-center">
                        <HiCheckCircle className="mr-1" />
                        Asistió
                    </span>
                ) : (
                    <span className="text-gray-500">Pendiente</span>
                )
            },
        },
        {
            header: 'Acciones',
            id: 'actions',
            cell: (props) => {
                const p = props.row.original
                const attended = hasAttended(p)
                return (
                    <div className="flex space-x-2">
                        {!attended ? (
                            <Button
                                size="sm"
                                variant="solid"
                                color="gray"
                                icon={<HiCheck />}
                                onClick={() => handleMark(p)}
                            >
                                Asistió
                            </Button>
                        ) : (
                            <Button
                                size="sm"
                                variant="solid"
                                color="red"
                                icon={<HiX />}
                                onClick={() => handleUnmark(p)}
                            >
                                Revertir
                            </Button>
                        )}
                    </div>
                )
            },
        },
    ]

    return (
        <div className="container mx-auto p-4 space-y-4">
            <div className="flex justify-between items-center mb-4">
                <div className="flex items-center space-x-2">
                    <Button
                        icon={<HiArrowLeft />}
                        variant="plain"
                        onClick={navigateBack}
                    >
                        {getBackButtonText()}
                    </Button>
                    <h1 className="text-2xl font-bold">
                        Asistencia Rápida: {event?.title}
                    </h1>
                </div>
                <div className="flex space-x-2">
                    <Button
                        size="sm"
                        variant="plain"
                        icon={<HiDownload />}
                        onClick={() => event && exportEventParticipants(event)}
                        disabled={!event}
                        title="Descargar participantes"
                    />
                    <Button
                        size="sm"
                        variant="plain"
                        icon={<HiUserAdd />}
                        onClick={() => {
                            // Navegar al registro de participantes preservando el contexto de navegación
                            navigate(`/events/${id}/register-participant`, {
                                state: {
                                    navigationContext: {
                                        origin: 'fast-attendance',
                                        returnPath: `/events/${id}/sessions/${sessionId}/asistencia-rapida`,
                                        eventId: id,
                                        sessionId: sessionId
                                    }
                                }
                            })
                        }}
                        disabled={!event}
                        title="Registrar nuevo participante"
                    />
                </div>
            </div>
            <Card className="p-4 space-y-4">
                <div className="flex justify-between items-center mb-4">
                    <Input
                        prefix={<HiSearch />}
                        size="lg"
                        placeholder="Buscar participante"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="flex-1 mr-4"
                    />
                    <Button
                        size="lg"
                        variant="solid"
                        color="blue"
                        onClick={handleFinishRegistration}
                        className="whitespace-nowrap"
                    >
                        Finalizar Registro
                    </Button>
                </div>
                <DataTable columns={columns} data={filteredParticipants} />
            </Card>

            <Dialog
                isOpen={showConfirmDialog}
                onClose={cancelFinishRegistration}
            >
                <div className="p-6">
                    <div className="flex items-center mb-4">
                        <HiExclamation className="text-amber-500 text-2xl mr-3" />
                        <h3 className="text-lg font-semibold">
                            Confirmar finalización
                        </h3>
                    </div>
                    <p className="mb-6 text-gray-600">
                        ¿Está seguro de que desea finalizar el registro de
                        asistencia?
                        {currentSession?.attendanceMode === 'kiosk' && (
                            <span className="block mt-2 text-amber-600 font-medium">
                                Nota: Los participantes que no fueron
                                registrados como presentes serán marcados
                                automáticamente como ausentes.
                            </span>
                        )}
                        Esta acción no se puede deshacer.
                    </p>
                    <div className="flex justify-end space-x-3">
                        <Button
                            variant="plain"
                            onClick={cancelFinishRegistration}
                        >
                            Cancelar
                        </Button>
                        <Button
                            variant="solid"
                            color="blue"
                            onClick={confirmFinishRegistration}
                        >
                            Finalizar
                        </Button>
                    </div>
                </div>
            </Dialog>
        </div>
    )
}

export default FastAttendanceView
