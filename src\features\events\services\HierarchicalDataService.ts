import ApiService from '@/shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import type { HierarchicalFilterData } from '../types'

/**
 * Interfaz para representar un campo/asociación
 */
export interface Field {
    id: string | number
    documentId?: string
    name: string
    acronym?: string
    type?: string
}

/**
 * Interfaz para representar una zona
 */
export interface Zone {
    id: string | number
    documentId?: string
    name: string
    field?: Field
}

/**
 * Interfaz para representar un distrito
 */
export interface District {
    id: string | number
    documentId?: string
    name: string
    zone?: Zone
}

/**
 * Interfaz para representar una iglesia
 */
export interface Church {
    id: string | number
    documentId?: string
    name: string
    address?: string
    phone?: string
    email?: string
    district?: District
}

/**
 * Respuesta de la API para datos jerárquicos
 */
export interface HierarchicalDataResponse {
    fields: Field[]
    zones: Zone[]
    districts: District[]
    churches: Church[]
    ecclesiasticalRoles: string[]
}

/**
 * Servicio para gestionar datos jerárquicos de la estructura eclesiástica
 * 
 * Este servicio maneja la obtención de datos de campos, zonas, distritos e iglesias
 * desde la API de Strapi, proporcionando métodos para filtrado en cascada y
 * manejo de dependencias jerárquicas.
 * 
 * Características principales:
 * - Obtención de datos jerárquicos completos desde la API
 * - Filtrado en cascada (campo → zona → distrito → iglesia)
 * - Caché de datos para optimizar rendimiento
 * - Manejo de errores y fallbacks
 */
const HierarchicalDataService = {
    /**
     * Obtiene todos los datos jerárquicos desde la API
     * Utiliza el endpoint específico que devuelve toda la estructura
     * 
     * @returns Promesa con los datos jerárquicos organizados
     * @throws Error si no se pueden obtener los datos de la API
     */
    async getHierarchicalData(): Promise<HierarchicalFilterData> {
        try {
            const response = await ApiService.fetchData<HierarchicalDataResponse>({
                url: API_ENDPOINTS.EVENTS.HIERARCHICAL_DATA,
                method: 'get',
            })

            const data = response.data

            // Transformar los datos al formato esperado por el frontend
            return {
                fields: data.fields.map(field => ({
                    id: field.id,
                    name: field.name,
                    acronym: field.acronym || '',
                    type: field.type || 'field'
                })),
                zones: data.zones.map(zone => ({
                    id: zone.id,
                    name: zone.name,
                    fieldId: zone.field?.id || zone.field || null
                })),
                districts: data.districts.map(district => ({
                    id: district.id,
                    name: district.name,
                    zoneId: district.zone?.id || district.zone || null
                })),
                churches: data.churches.map(church => ({
                    id: church.id,
                    name: church.name,
                    districtId: church.district?.id || church.district || null
                })),
                ecclesiasticalRoles: data.ecclesiasticalRoles || []
            }
        } catch (error) {
            console.error('Error al obtener datos jerárquicos:', error)
            throw new Error('No se pudieron obtener los datos jerárquicos')
        }
    },

    /**
     * Obtiene todos los campos/asociaciones
     * 
     * @returns Promesa con la lista de campos
     */
    async getFields(): Promise<Field[]> {
        try {
            const response = await ApiService.fetchData<{ data: Field[] }>({
                url: API_ENDPOINTS.FIELDS.BASE,
                method: 'get',
            })

            return response.data.data || []
        } catch (error) {
            console.error('Error al obtener campos:', error)
            throw new Error('No se pudieron obtener los campos')
        }
    },

    /**
     * Obtiene las zonas filtradas por campo
     * 
     * @param fieldId - ID del campo para filtrar las zonas
     * @returns Promesa con la lista de zonas del campo especificado
     */
    async getZonesByField(fieldId: string | number): Promise<Zone[]> {
        try {
            const response = await ApiService.fetchData<{ data: Zone[] }>({
                url: API_ENDPOINTS.ZONES.BY_FIELD(fieldId),
                method: 'get',
            })

            return response.data.data || []
        } catch (error) {
            console.error('Error al obtener zonas por campo:', error)
            throw new Error('No se pudieron obtener las zonas')
        }
    },

    /**
     * Obtiene los distritos filtrados por zona
     * 
     * @param zoneId - ID de la zona para filtrar los distritos
     * @returns Promesa con la lista de distritos de la zona especificada
     */
    async getDistrictsByZone(zoneId: string | number): Promise<District[]> {
        try {
            const response = await ApiService.fetchData<{ data: District[] }>({
                url: API_ENDPOINTS.DISTRICTS.BY_ZONE(zoneId),
                method: 'get',
            })

            return response.data.data || []
        } catch (error) {
            console.error('Error al obtener distritos por zona:', error)
            throw new Error('No se pudieron obtener los distritos')
        }
    },

    /**
     * Obtiene las iglesias filtradas por distrito
     * 
     * @param districtId - ID del distrito para filtrar las iglesias
     * @returns Promesa con la lista de iglesias del distrito especificado
     */
    async getChurchesByDistrict(districtId: string | number): Promise<Church[]> {
        try {
            const response = await ApiService.fetchData<{ data: Church[] }>({
                url: API_ENDPOINTS.CHURCHES.BY_DISTRICT(districtId),
                method: 'get',
            })

            return response.data.data || []
        } catch (error) {
            console.error('Error al obtener iglesias por distrito:', error)
            throw new Error('No se pudieron obtener las iglesias')
        }
    },

    /**
     * Obtiene los roles eclesiásticos únicos desde la API de usuarios
     * 
     * @returns Promesa con la lista de roles eclesiásticos únicos
     */
    async getEcclesiasticalRoles(): Promise<string[]> {
        try {
            const response = await ApiService.fetchData<{ data: Array<{ ecclesiasticalRole?: string }> }>({
                url: API_ENDPOINTS.PARTICIPANT_SEARCH.ECCLESIASTICAL_ROLES,
                method: 'get',
            })

            // Extraer roles únicos y filtrar valores vacíos
            const roles = response.data.data
                .map(user => user.ecclesiasticalRole)
                .filter((role): role is string => Boolean(role))
                .filter((role, index, array) => array.indexOf(role) === index)
                .sort()

            return roles
        } catch (error) {
            console.error('Error al obtener roles eclesiásticos:', error)
            throw new Error('No se pudieron obtener los roles eclesiásticos')
        }
    }
}

export default HierarchicalDataService
