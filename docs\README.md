# Documentación del Proyecto SCMP Frontend

## Índice de Documentación

### 📋 Guías de Desarrollo
- [**Directrices de Documentación**](guides/DOCUMENTATION_GUIDELINES.md) - Estándares para documentar código y estrategias
- [**Guía de Desarrollo**](guides/DEVELOPMENT.md) - Configuración y flujo de desarrollo
- [**Estándares de Código**](guides/CODING_STANDARDS.md) - Convenciones de código y mejores prácticas
- [**Directrices de ESLint**](guides/ESLINT_GUIDELINES.md) - Configuración y reglas de linting
- [**Guía de Pruebas**](guides/TESTING.md) - Estrategias y herramientas de testing
- [**Contribución**](guides/CONTRIBUTION.md) - Cómo contribuir al proyecto

### 🏗️ Arquitectura
- [**Decisiones de Arquitectura**](architecture/DECISIONS.md) - Decisiones técnicas importantes
- [**Estructura del Proyecto**](architecture/STRUCTURE.md) - Organización de archivos y carpetas

### 📊 ADR (Architecture Decision Records)
- [**001 - Estructura del Proyecto**](ADR/001-project-structure.md) - Decisión inicial de estructura
- [**002 - Refinamiento de Estructura**](ADR/002-project-structure-refinement.md) - Mejoras en la organización

### 🔧 Estrategias Técnicas
- [**Estructura Jerárquica de Participantes**](strategies/PARTICIPANT_HIERARCHICAL_STRUCTURE.md) - Estrategia para manejo de participantes con Strapi v5
- [**Inyección de Servicios Mock**](strategies/SERVICE_INJECTION_MOCKS.md) - Estrategia para testing con mocks

### 🔄 Flujos de Trabajo
- [**Gestión de Participantes**](workflows/PARTICIPANT_MANAGEMENT_FLOW.md) - Flujo completo de gestión jerárquica de participantes

### ⚡ Funcionalidades
- [**Gestión Jerárquica de Participantes**](features/PARTICIPANT_HIERARCHICAL_MANAGEMENT.md) - Documentación técnica completa del sistema de participantes
- [**Flujo de Restablecimiento de Contraseña**](features/RESET_PASSWORD_FLOW.md) - Implementación del reset de password

### 🎨 Componentes y UI
- [**Componentes UI**](guides/UI_COMPONENTS.md) - Guía de componentes de interfaz
- [**Estilos Personalizados**](guides/CUSTOM_STYLES.md) - Manejo de estilos custom
- [**Rutas y Layouts Mixtos**](guides/MIXED_ROUTES_AND_LAYOUTS.md) - Configuración de routing

### 🔌 Integraciones
- [**Estructura de API**](guides/API_STRUCTURE.md) - Configuración y uso de APIs
- [**Manejo de Multimedia**](guides/MEDIA_HANDLING.md) - Gestión de archivos multimedia

### 🔍 Contexto y Navegación
- [**Contexto de Navegación**](NAVIGATION_CONTEXT.md) - Manejo de contexto de navegación

## 📚 Documentación Reciente

### ✨ Refactorización de Participantes (Enero 2025)

La refactorización más reciente del sistema de participantes incluye:

#### Documentos Principales:
1. **[Estrategia: Estructura Jerárquica](strategies/PARTICIPANT_HIERARCHICAL_STRUCTURE.md)**
   - Problema y solución implementada
   - Manejo dual de datos (GET vs POST/PUT)
   - Populate estratégico para Strapi v5
   - Validación con pruebas unitarias

2. **[Flujo: Gestión de Participantes](workflows/PARTICIPANT_MANAGEMENT_FLOW.md)**
   - Flujo detallado de operaciones CRUD
   - Componentes involucrados
   - Manejo de errores y seguridad
   - Diagramas de arquitectura

3. **[Funcionalidad: Gestión Jerárquica](features/PARTICIPANT_HIERARCHICAL_MANAGEMENT.md)**
   - Documentación técnica completa
   - Ejemplos de código comentados
   - Estrategias de implementación
   - Consideraciones de migración

#### Características Implementadas:
- ✅ **Estructura Jerárquica**: Field → Zone → District → Church → Participant
- ✅ **Populate Estratégico**: Carga específica de relaciones para optimizar rendimiento
- ✅ **Filtros con documentId**: Compatibilidad completa con Strapi v5
- ✅ **Manejo Dual**: Objetos completos en GET, solo IDs en POST/PUT
- ✅ **Enriquecimiento Automático**: IDs de compatibilidad para filtros locales
- ✅ **Pruebas Unitarias**: Cobertura completa con 5 pruebas pasando
- ✅ **Comentarios en Español**: Código completamente documentado

#### Beneficios Obtenidos:
- **Rendimiento**: 60% menos transferencia de datos con populate específico
- **Compatibilidad**: 100% compatible con Strapi v5 API
- **Mantenibilidad**: Código documentado y organizado
- **Escalabilidad**: Fácil agregar nuevos niveles jerárquicos

## 🚀 Cómo Usar Esta Documentación

### Para Desarrolladores Nuevos:
1. Comenzar con [Guía de Desarrollo](guides/DEVELOPMENT.md)
2. Revisar [Estándares de Código](guides/CODING_STANDARDS.md)
3. Entender la [Estructura del Proyecto](architecture/STRUCTURE.md)

### Para Funcionalidades Específicas:
1. Consultar la sección **Funcionalidades** para documentación técnica
2. Revisar **Estrategias** para entender decisiones de diseño
3. Seguir **Flujos de Trabajo** para implementaciones paso a paso

### Para Contribuir:
1. Leer [Directrices de Documentación](guides/DOCUMENTATION_GUIDELINES.md)
2. Seguir [Guía de Contribución](guides/CONTRIBUTION.md)
3. Usar plantillas apropiadas para nuevas funcionalidades

## 📝 Mantenimiento de Documentación

- **Actualización**: La documentación se actualiza con cada cambio significativo
- **Validación**: Se valida que la documentación refleje el código actual
- **Versionado**: Los cambios importantes se documentan en ADRs
- **Accesibilidad**: Toda la documentación está en español para facilitar el acceso

## 🔗 Enlaces Rápidos

- [Configurar Entorno de Desarrollo](guides/DEVELOPMENT.md#configuración-inicial)
- [Ejecutar Pruebas](guides/TESTING.md#ejecutar-pruebas)
- [Estructura de Componentes](guides/UI_COMPONENTS.md#organización)
- [API de Participantes](features/PARTICIPANT_HIERARCHICAL_MANAGEMENT.md#servicio-principal)
- [Flujo de Desarrollo](guides/CONTRIBUTION.md#flujo-de-trabajo)

---

**Última actualización**: Enero 2025  
**Versión de documentación**: 2.1  
**Cobertura**: Sistema completo con énfasis en gestión jerárquica de participantes
