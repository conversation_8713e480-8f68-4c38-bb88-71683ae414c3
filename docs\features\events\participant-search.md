# Búsqueda de Participantes en Eventos

## Descripción General

La funcionalidad de búsqueda de participantes permite filtrar y seleccionar participantes para eventos utilizando filtros jerárquicos y búsqueda por texto libre. Esta funcionalidad se integra con la API real de Strapi v5 y proporciona un sistema de filtrado en cascada basado en la estructura eclesiástica.

## Arquitectura

### Servicios

#### HierarchicalDataService
**Ubicación**: `src/features/events/services/HierarchicalDataService.ts`

Servicio responsable de obtener datos jerárquicos de la estructura eclesiástica desde la API.

**Métodos principales**:
- `getHierarchicalData()`: Obtiene todos los datos jerárquicos organizados
- `getFields()`: Obtiene lista de campos/asociaciones
- `getZonesByField(fieldId)`: Obtiene zonas filtradas por campo
- `getDistrictsByZone(zoneId)`: Obtiene distritos filtrados por zona
- `getChurchesByDistrict(districtId)`: Obtiene iglesias filtradas por distrito
- `getEcclesiasticalRoles()`: Obtiene roles eclesiásticos únicos

**Características**:
- Integración con API de Strapi v5
- Manejo de errores con fallbacks
- Transformación de datos al formato esperado por el frontend
- Uso de documentId para compatibilidad con Strapi v5

#### ParticipantsService (Actualizado)
**Ubicación**: `src/features/events/services/ParticipantsService.ts`

Servicio extendido para soportar búsqueda con filtros jerárquicos.

**Nuevos métodos**:
- `searchParticipantsWithFilters(searchTerm, hierarchicalFilters)`: Búsqueda combinada
- `getEcclesiasticalRoles()`: Obtiene roles eclesiásticos únicos

### Tipos y Constantes

#### Cargos Eclesiásticos
**Ubicación**: `src/features/events/types/participant.ts`

```typescript
export const ECCLESIASTICAL_ROLES = {
    PASTOR: 'Pastor',
    PASTOR_DISTRITAL: 'Pastor Distrital',
    ANCIANO: 'Anciano',
    DIACONO: 'Diácono',
    DEPARTAMENTAL: 'Departamental',
    // ... más roles
} as const

export type EcclesiasticalRoleType = typeof ECCLESIASTICAL_ROLES[keyof typeof ECCLESIASTICAL_ROLES]
export const ECCLESIASTICAL_ROLES_LIST = Object.values(ECCLESIASTICAL_ROLES)
```

#### Endpoints de API
**Ubicación**: `src/shared/constants/api.constant.ts`

Nuevos endpoints agregados:
- `HIERARCHICAL_DATA`: Datos jerárquicos completos
- `FIELDS`: Gestión de campos/asociaciones
- `ZONES`: Gestión de zonas pastorales
- `DISTRICTS`: Gestión de distritos
- `CHURCHES`: Gestión de iglesias locales
- `PARTICIPANT_SEARCH`: Búsqueda de participantes

## Funcionalidad del Modal

### Filtrado en Cascada

El sistema implementa un filtrado jerárquico en cascada:

1. **Campo Eclesiástico** (obligatorio)
   - Selección inicial que habilita los demás filtros
   - Filtra automáticamente las zonas disponibles

2. **Zona Pastoral** (opcional)
   - Se filtra por el campo seleccionado
   - Al seleccionar, filtra los distritos disponibles

3. **Distrito Pastoral** (opcional)
   - Se filtra por la zona seleccionada
   - Al seleccionar, filtra las iglesias disponibles

4. **Iglesia Local** (opcional)
   - Se filtra por el distrito seleccionado
   - Nivel más específico de filtrado

5. **Cargo/Ministerio** (opcional)
   - Lista de roles eclesiásticos únicos
   - Independiente de la jerarquía geográfica

### Búsqueda Combinada

La búsqueda combina dos tipos de filtros:

1. **Búsqueda por texto libre**:
   - Nombre del participante
   - Apellido del participante
   - Email del participante
   - Cargo eclesiástico

2. **Filtros estructurales**:
   - Campo eclesiástico
   - Zona pastoral
   - Distrito pastoral
   - Iglesia local
   - Cargo/ministerio específico

### Estados y Comportamiento

#### Estados de Carga
- `isLoadingHierarchicalData`: Carga inicial de datos jerárquicos
- `loadingParticipants`: Búsqueda de participantes en progreso

#### Reseteo Automático
- Al cambiar campo → resetea zona, distrito e iglesia
- Al cambiar zona → resetea distrito e iglesia
- Al cambiar distrito → resetea iglesia

#### Manejo de Errores
- Fallback automático a datos mockeados si la API falla
- Notificaciones de error al usuario
- Logs detallados para debugging

## Integración con EventFormView

### Estados del Componente

```typescript
// Filtros del modal
const [searchTerm, setSearchTerm] = useState('')
const [selectedField, setSelectedField] = useState<string | number | null>(null)
const [selectedZone, setSelectedZone] = useState<string | number | null>(null)
const [selectedDistrict, setSelectedDistrict] = useState<string | number | null>(null)
const [selectedChurch, setSelectedChurch] = useState<string | number | null>(null)
const [selectedRole, setSelectedRole] = useState<string | number | null>(null)

// Datos jerárquicos
const [hierarchicalData, setHierarchicalData] = useState<HierarchicalFilterData | null>(null)
const [isLoadingHierarchicalData, setIsLoadingHierarchicalData] = useState(false)

// Participantes filtrados
const [filteredModalParticipants, setFilteredModalParticipants] = useState<Participant[]>([])
```

### Funciones Principales

#### `handleApplyFiltersInModal()`
Función principal que ejecuta la búsqueda con filtros combinados:

```typescript
const handleApplyFiltersInModal = async () => {
    setLoadingParticipants(true)
    
    try {
        const hierarchicalFilters = {
            fieldId: selectedField,
            zoneId: selectedZone,
            districtId: selectedDistrict,
            churchId: selectedChurch,
            ecclesiasticalRole: typeof selectedRole === 'string' ? selectedRole : null
        }

        const participants = await ParticipantsService.searchParticipantsWithFilters(
            searchTerm || undefined,
            hierarchicalFilters
        )

        setFilteredModalParticipants(participants)
    } catch (error) {
        // Manejo de errores con fallback
    } finally {
        setLoadingParticipants(false)
    }
}
```

## Consideraciones de Rendimiento

### Optimizaciones Implementadas
1. **Carga bajo demanda**: Los datos jerárquicos se cargan solo al abrir el modal
2. **Filtrado en frontend**: Los dropdowns se filtran localmente para mejor UX
3. **Estados de carga**: Indicadores visuales durante las operaciones
4. **Fallbacks**: Datos mockeados como respaldo en caso de errores

### Recomendaciones
1. **Paginación**: Implementar paginación para grandes datasets
2. **Debouncing**: Agregar debounce a la búsqueda por texto
3. **Caché**: Considerar caché de datos jerárquicos
4. **Lazy loading**: Cargar datos jerárquicos solo cuando sea necesario

## Pruebas Recomendadas

### Pruebas Unitarias
- Servicios de datos jerárquicos
- Funciones de filtrado
- Transformación de datos

### Pruebas de Integración
- Flujo completo de búsqueda
- Filtrado en cascada
- Manejo de errores

### Pruebas de Usuario
- Usabilidad del modal
- Rendimiento con datos reales
- Comportamiento en diferentes dispositivos

## Mantenimiento

### Archivos a Monitorear
- `HierarchicalDataService.ts`: Cambios en la API
- `api.constant.ts`: Nuevos endpoints
- `participant.ts`: Nuevos tipos de cargos
- `EventFormView.tsx`: Lógica del modal

### Actualizaciones Futuras
- Nuevos niveles jerárquicos
- Filtros adicionales
- Mejoras de rendimiento
- Nuevos tipos de cargos eclesiásticos
