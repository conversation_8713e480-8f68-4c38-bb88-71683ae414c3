import type { Media } from '@/shared/types/media'

/**
 * Tipos de cargos/ministerios eclesiásticos más comunes
 * Estos son los valores más frecuentes, pero el campo ecclesiasticalRole
 * sigue siendo string para permitir flexibilidad
 */
export const ECCLESIASTICAL_ROLES = {
    PASTOR: 'Pastor',
    PASTOR_DISTRITAL: 'Pastor <PERSON>',
    PASTOR_ASOCIADO: 'Pastor <PERSON>',
    ANCIANO: 'Ancian<PERSON>',
    ANCIANA: 'Anciana',
    DIACONO: 'Diácono',
    DIACONISA: 'Diaconisa',
    DEPARTAMENTAL: 'Departamental',
    DIRECTOR_DEPARTAMENTAL: 'Director Departamental',
    SECRETARIO: 'Secretario',
    TESORERO: 'Tesorero',
    MIEMBRO: 'Miembro',
    INVITADO: 'Invitado',
    LIDER_JOVEN: 'Líder Joven',
    MAESTRO_ESCUELA_SABATICA: 'Maestro Escuela Sabática',
    COORDINADOR: 'Coordinador'
} as const

/**
 * Tipo derivado de los valores de cargos eclesiásticos
 */
export type EcclesiasticalRoleType = typeof ECCLESIASTICAL_ROLES[keyof typeof ECCLESIASTICAL_ROLES]

/**
 * Lista de cargos eclesiásticos como array para uso en componentes
 */
export const ECCLESIASTICAL_ROLES_LIST = Object.values(ECCLESIASTICAL_ROLES)

/**
 * Entidades jerárquicas básicas
 */
export interface Field {
    id: number
    documentId: string
    name: string
    acronym?: string
    type: string
}

export interface Zone {
    id: number
    documentId: string
    name: string
    field?: {
        id: number
        documentId: string
    }
}

export interface District {
    id: number
    documentId: string
    name: string
    zone?: {
        id: number
        documentId: string
    }
}

export interface Church {
    id: number
    documentId: string
    name: string
    district?: {
        id: number
        documentId: string
    }
}

/**
 * Cuenta de usuario vinculada (opcional)
 */
export interface UserAccount {
    id: number
    documentId: string
    username?: string
    email: string
    firstName?: string
    lastName?: string
    phone?: string
    ecclesiasticalRole?: string
    avatar?: Media
}

/**
 * Interfaz para representar un participante de evento (respuesta GET de Strapi)
 * Los participantes son entidades independientes que pueden o no estar vinculadas a usuarios del sistema
 */
export interface Participant {
    /** ID único del participante */
    id: string | number

    /** Document ID de Strapi v5 */
    documentId?: string

    /** Nombre del participante */
    firstName: string

    /** Apellido del participante */
    lastName: string

    /** Email del participante (único) */
    email: string

    /** Número de teléfono (opcional) */
    phone?: string

    /** Cargo eclesiástico del participante */
    ecclesiasticalRole?: string

    /** Avatar del participante */
    avatar?: Media | string | null

    /** Cuenta de usuario vinculada (objeto completo cuando se hace populate) */
    userAccount?: UserAccount | null

    /** Asignaciones jerárquicas (objetos completos cuando se hace populate) */
    fieldAssignment?: Field | null
    zoneAssignment?: Zone | null
    districtAssignment?: District | null
    churchAssignment?: Church | null

    /** IDs de las asignaciones jerárquicas (para compatibilidad y filtros) */
    fieldAssignmentId?: number | string | null
    zoneAssignmentId?: number | string | null
    districtAssignmentId?: number | string | null
    churchAssignmentId?: number | string | null

    /** Estado de asistencia (para eventos) */
    attended?: boolean | null

    /** Timestamps de Strapi */
    createdAt?: string
    updatedAt?: string
    publishedAt?: string
}

/**
 * Datos requeridos para crear un nuevo participante (POST request)
 * Solo se envían IDs para las relaciones con otros modelos
 */
export interface CreateParticipantRequest {
    firstName: string
    lastName: string
    email: string
    phone?: string
    ecclesiasticalRole?: string
    /** Solo IDs para las relaciones - Strapi maneja automáticamente las relaciones */
    fieldAssignment?: number | string | null
    zoneAssignment?: number | string | null
    districtAssignment?: number | string | null
    churchAssignment?: number | string | null
    userAccount?: number | string | null
}

/**
 * Datos para actualizar un participante existente (PUT request)
 * En Strapi v5, el documentId va en la URL, no en el body
 * Solo se envían IDs para las relaciones con otros modelos
 */
export interface UpdateParticipantRequest {
    firstName?: string
    lastName?: string
    email?: string
    phone?: string
    ecclesiasticalRole?: string
    /** Solo IDs para las relaciones - Strapi maneja automáticamente las relaciones */
    fieldAssignment?: number | string | null
    zoneAssignment?: number | string | null
    districtAssignment?: number | string | null
    churchAssignment?: number | string | null
    userAccount?: number | string | null
}

/**
 * Filtros para buscar participantes
 */
export interface ParticipantFilters {
    search?: string
    email?: string
    firstName?: string
    lastName?: string
    ecclesiasticalRole?: string
    fieldAssignmentId?: number | string | null
    zoneAssignmentId?: number | string | null
    districtAssignmentId?: number | string | null
    churchAssignmentId?: number | string | null
}

/**
 * Respuesta de la API para participantes
 */
export interface ParticipantResponse {
    data: Participant[]
    meta?: {
        pagination?: {
            page: number
            pageSize: number
            pageCount: number
            total: number
        }
    }
}

/**
 * Respuesta de la API para un solo participante
 */
export interface SingleParticipantResponse {
    data: Participant
}

/**
 * Funciones utilitarias para transformar datos entre formatos GET y POST
 */

/**
 * Extrae los IDs de las asignaciones jerárquicas de un participante
 * Útil para preparar datos para POST/PUT requests
 */
export function extractHierarchicalIds(participant: Participant): {
    fieldAssignment?: number | string | null
    zoneAssignment?: number | string | null
    districtAssignment?: number | string | null
    churchAssignment?: number | string | null
} {
    return {
        fieldAssignment: participant.fieldAssignment?.documentId || participant.fieldAssignmentId || null,
        zoneAssignment: participant.zoneAssignment?.documentId || participant.zoneAssignmentId || null,
        districtAssignment: participant.districtAssignment?.documentId || participant.districtAssignmentId || null,
        churchAssignment: participant.churchAssignment?.documentId || participant.churchAssignmentId || null,
    }
}

/**
 * Convierte un participante completo a formato de request para crear/actualizar
 */
export function participantToCreateRequest(participant: Participant): CreateParticipantRequest {
    const hierarchicalIds = extractHierarchicalIds(participant)

    return {
        firstName: participant.firstName,
        lastName: participant.lastName,
        email: participant.email,
        phone: participant.phone,
        ecclesiasticalRole: participant.ecclesiasticalRole,
        userAccount: participant.userAccount?.documentId || null,
        ...hierarchicalIds
    }
}

/**
 * Convierte un participante completo a formato de request para actualizar
 */
export function participantToUpdateRequest(participant: Partial<Participant>): UpdateParticipantRequest {
    const hierarchicalIds = participant.fieldAssignment || participant.zoneAssignment ||
                           participant.districtAssignment || participant.churchAssignment
                           ? extractHierarchicalIds(participant as Participant)
                           : {}

    return {
        firstName: participant.firstName,
        lastName: participant.lastName,
        email: participant.email,
        phone: participant.phone,
        ecclesiasticalRole: participant.ecclesiasticalRole,
        userAccount: participant.userAccount?.documentId || null,
        ...hierarchicalIds
    }
}

/**
 * Enriquece un participante con nombres de asignaciones jerárquicas para mostrar en UI
 */
export function enrichParticipantWithNames(participant: Participant): Participant {
    return {
        ...participant,
        fieldAssignmentId: participant.fieldAssignment?.documentId || participant.fieldAssignmentId,
        zoneAssignmentId: participant.zoneAssignment?.documentId || participant.zoneAssignmentId,
        districtAssignmentId: participant.districtAssignment?.documentId || participant.districtAssignmentId,
        churchAssignmentId: participant.churchAssignment?.documentId || participant.churchAssignmentId,
    }
}
