/**
 * Tipos para el módulo de eventos
 * Define las interfaces y tipos utilizados en el módulo de eventos
 */
import type { UserProfile } from '@/features/account/types/account';
import type { Media } from '@/shared/types/media';
import type { AttendanceSession, AttendanceSessionStatus } from './attendanceSession';
import type {
    Participant,
    Field,
    Zone,
    District,
    Church,
    UserAccount,
    CreateParticipantRequest,
    UpdateParticipantRequest,
    ParticipantFilters,
    ParticipantResponse,
    SingleParticipantResponse,
    extractHierarchicalIds,
    participantToCreateRequest,
    participantToUpdateRequest,
    enrichParticipantWithNames
} from './participant';

/**
 * @deprecated Use Participant instead
 * Mantenido para compatibilidad con código existente
 */

/**
 * Estados posibles de un evento
 */
export type EventStatus = 'programada' | 'en-progreso' | 'completada' | 'cancelada';

// Re-exportar tipos para facilitar el acceso
export type { AttendanceSessionStatus } from './attendanceSession';
export type {
    Participant,
    EcclesiasticalRoleType,
    ECCLESIASTICAL_ROLES,
    ECCLESIASTICAL_ROLES_LIST
} from './participant';

// Alias para compatibilidad con código existente
export type EventParticipant = Participant;

/**
 * Tipos de eventos disponibles
 */
export interface EventType {
    id: string | number;
    name: string;
    autoRegistrationDefault: boolean;
    attendanceMethodDefault: 'manual' | 'kiosco_rapido';
}

/**
 * Interfaz para representar un departamento
 */
export interface Department {
    id: string | number;
    name: string;
    description?: string;
    active: boolean;
    createdAt?: string;
    updatedAt?: string;
}

/**
 * Interfaz para estadísticas de un departamento
 */
export interface DepartmentStats {
    totalEvents: number;
    activeEvents: number;
    completedEvents: number;
    averageAttendance: number;
}

/**
 * Resultado de la validación de un participante
 */
export interface ParticipantValidationResult {
    // Estado de existencia
    existsInSystem: boolean;
    existsInEvent: boolean;
    
    // Datos del participante existente (si existe)
    existingParticipant?: Participant;
    
    // Diferencias encontradas en los datos
    hasDataDifferences: boolean;
    dataDifferences?: {
        fieldAssignment?: boolean;
        zoneAssignment?: boolean;
        districtAssignment?: boolean;
        churchAssignment?: boolean;
        ecclesiasticalRole?: boolean;
        phone?: boolean;
        firstName?: boolean;
        lastName?: boolean;
    };
    
    // Información sobre fotos
    hasExistingPhoto: boolean;
    willUpdatePhoto: boolean;
    
    // Mensajes para mostrar al usuario
    message: string;
    actionRequired: 'none' | 'confirm_update' | 'already_registered';
}

/**
 * Datos del participante para validación
 */
export interface ParticipantValidationData {
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
    fieldAssignmentId?: string | number | null;
    zoneAssignmentId?: string | number | null;
    districtAssignmentId?: string | number | null;
    churchAssignmentId?: string | number | null;
    ecclesiasticalRole?: string | number | null;
    photo?: File | null;
}

/**
 * Estructura de un registro de asistencia para el componente en Strapi.
 */
export interface AttendanceRecord {
    id?: number | string; // ID del registro de asistencia en sí
    sessionId: string | number;
    person: Pick<Participant, 'id' | 'firstName' | 'lastName' | 'ecclesiasticalRole' | 'avatar' | 'email'>; // Referencia al participante
    attended: boolean;
    notes?: string; // Para la razón de ausencia
    partialAttendance?: boolean; // Indica si la asistencia fue parcial
    partialAttendanceComment?: string; // Comentario sobre la asistencia parcial
    timeStayed?: string; // Tiempo aproximado que estuvo en el evento
}

/**
 * Estructura de un evento
 */
export interface Event {
    id: number | string;
    documentId: string; // ID de documento de Strapi v5 - requerido para operaciones específicas de la API
    title: string;
    subject?: string; // Grupo/Ministerio
    topic?: string; // Tema Específico
    description?: string; // Podría ser richtext
    date: string; // YYYY-MM-DD (fecha de inicio)
    endDate?: string; // YYYY-MM-DD (fecha de fin para eventos de múltiples días)
    isMultiDay?: boolean; // Indica si el evento dura múltiples días
    hasMultipleSessions?: boolean; // Indica si el evento tiene múltiples sesiones de asistencia
    startTime: string; // HH:mm
    endTime?: string; // HH:mm
    location: string;
    eventType?: EventType;
    autoRegistration?: boolean;
    eventStatus? :EventStatus| 'programada' | 'en-progreso' | 'completada' | 'cancelada'; // Estado general del evento (calculado a partir de las sesiones)
    sendNotifications?: boolean;
    invitingDepartment?: string; 
    atendanceMethod?: string | 'manual' | 'kiosco_rapido'; // Método de asistencia
    
    // Lista de participantes invitados al evento
    // Ahora usa el nuevo tipo Participant que es independiente de los usuarios del sistema
    participantsInvited: Participant[];
    
    // Para la API real, esto vendría como un array de componentes.
    // Para el mock, podemos simularlo así.
    attendanceRecords?: AttendanceRecord[];
    sessions: AttendanceSession[];

    // Campo temporal para almacenar el registro de asistencia del usuario actual
    // Solo se usa en el frontend para facilitar la visualización
    userAttendanceRecord?: AttendanceRecord;
}

/**
 * Define la estructura jerárquica de la organización eclesiástica para los filtros.
 * (Esta estructura es principalmente para la lógica del frontend en el prototipo)
 */
export interface EcclesiasticalUnit {
    id: string | number;
    name: string;
    type: 'field' | 'zone' | 'district' | 'church';
    children?: EcclesiasticalUnit[];
    // Otros campos relevantes, como acronym para field
    acronym?: string;
}

/**
 * Estructura para popular los selects de filtros jerárquicos
 */
export type HierarchicalFilterData = {
    fields: Array<{ id: string | number; name: string; acronym?: string, type: string }>;
    zones: Array<{ id: string | number; name: string; fieldId: string | number }>;
    districts: Array<{ id: string | number; name: string; zoneId: string | number }>;
    churches: Array<{ id: string | number; name: string; districtId: string | number }>;
    // Lista de todos los puestos/roles eclesiásticos únicos para el filtro
    ecclesiasticalRoles: string[];
};

// Re-exportar tipos de participantes para facilitar el acceso
export type {
    Field,
    Zone,
    District,
    Church,
    UserAccount,
    CreateParticipantRequest,
    UpdateParticipantRequest,
    ParticipantFilters,
    ParticipantResponse,
    SingleParticipantResponse
};

// Re-exportar funciones utilitarias de participantes
export {
    extractHierarchicalIds,
    participantToCreateRequest,
    participantToUpdateRequest,
    enrichParticipantWithNames
};
