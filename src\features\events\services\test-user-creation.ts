/**
 * Script de prueba para verificar la creación de usuarios
 * Este archivo es temporal y se puede eliminar después de las pruebas
 */

import ParticipantsService from './ParticipantsService'

/**
 * Función de prueba para crear un usuario de ejemplo
 */
export const testUserCreation = async () => {
    try {
        console.log('🧪 Iniciando prueba de creación de usuario...')
        
        const testUserData = {
            email: '<EMAIL>',
            firstName: 'Usuario',
            lastName: 'De Prueba',
            phone: '+1234567890',
            ecclesiasticalRole: 'Miembro',
            fieldAssignmentId: 1,
            zoneAssignmentId: 1,
            districtAssignmentId: 1,
            churchAssignmentId: 1
        }

        console.log('📤 Enviando datos:', testUserData)
        
        const createdUser = await ParticipantsService.createUser(testUserData)
        
        console.log('✅ Usuario creado exitosamente:', createdUser)
        return createdUser
        
    } catch (error) {
        console.error('❌ Error en la prueba de creación de usuario:', error)
        throw error
    }
}

/**
 * Función de prueba para buscar un usuario por email
 */
export const testUserSearch = async (email: string) => {
    try {
        console.log('🔍 Buscando usuario por email:', email)
        
        const foundUser = await ParticipantsService.findUserByEmail(email)
        
        if (foundUser) {
            console.log('✅ Usuario encontrado:', foundUser)
        } else {
            console.log('ℹ️ Usuario no encontrado')
        }
        
        return foundUser
        
    } catch (error) {
        console.error('❌ Error en la búsqueda de usuario:', error)
        throw error
    }
}

/**
 * Función de prueba completa que simula el flujo de importación
 */
export const testImportFlow = async () => {
    try {
        console.log('🚀 Iniciando prueba completa del flujo de importación...')
        
        const testEmail = '<EMAIL>'
        
        // 1. Buscar si el usuario existe
        console.log('1️⃣ Verificando si el usuario existe...')
        const existingUser = await testUserSearch(testEmail)
        
        if (!existingUser) {
            // 2. Si no existe, crear el usuario
            console.log('2️⃣ Usuario no existe, creando nuevo usuario...')
            const newUser = await testUserCreation()
            console.log('✅ Flujo completado - Usuario creado')
            return newUser
        } else {
            console.log('ℹ️ Flujo completado - Usuario ya existía')
            return existingUser
        }
        
    } catch (error) {
        console.error('❌ Error en el flujo de prueba:', error)
        throw error
    }
}
