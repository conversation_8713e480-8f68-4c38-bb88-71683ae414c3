import ApiService from '@/shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import type {
    Event,
    Participant,
    HierarchicalFilterData,
    AttendanceRecord,
    ParticipantValidationResult,
    EventType,
} from '../types'
import type { IEventsService } from './EventsService.interface'
import type { PaginatedResponse } from '@/shared/types/api'
import {
    transformEventDataForStrapi,
    transformEventDataFromStrapi,
    logTransformation
} from '../utils/eventDataTransformers'

/**
 * Servicio que consume la API real para todas las operaciones relacionadas
 * con eventos.
 */
const EventsApiService: IEventsService = {
    /** Obtiene todos los eventos existentes */
    getEvents() {
        return ApiService.fetchData<PaginatedResponse<Event>>({
            url: API_ENDPOINTS.EVENTS.BASE,
            method: 'get',
        }).then((res) => {
            console.warn('🔍 DEBUG - API Response:', JSON.stringify(res))
            // La respuesta de Strapi viene como { data: { data: [...], meta: {...} } }
            // Necesitamos extraer el objeto interno que contiene data y meta
            return res.data
        })
    },

    /**
     * Obtiene un evento por su identificador
     * @param id - ID del evento (puede ser id numérico o documentId de Strapi v5)
     */
    getEventById(id) {
        // Para Strapi v5, intentamos usar documentId si está disponible
        // Si el id es un string que parece ser un documentId, usamos el endpoint específico

        // Verifica si contiene al menos una letra y al menos un número
        const regex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]+$/;
        const isDocumentId = typeof id === 'string' && regex.test(id)
        const endpoint = isDocumentId
            ? API_ENDPOINTS.EVENTS.BY_DOCUMENT_ID(id)
            : API_ENDPOINTS.EVENTS.BY_ID(id)

        return ApiService.fetchData< Event>({
            url: endpoint,
            method: 'get',
        }).then((res) => transformEventDataFromStrapi(res.data.data))
    },

    /**
     * Crea un nuevo evento en la API
     * @param eventData - Datos del evento a crear
     */
    createEvent(eventData) {
        // Transformar los datos al formato que espera Strapi
        const transformedData = transformEventDataForStrapi(eventData);
        
        // Log de la transformación para debugging
        if (process.env.NODE_ENV === 'development') {
            logTransformation(eventData, transformedData);
        }
        
        return ApiService.fetchData<any>({
            url: API_ENDPOINTS.EVENTS.BASE,
            method: 'post',
            data: { data: transformedData as Record<string, unknown>},
        }).then((res) => res.data)
    },

    /**
     * Actualiza un evento existente
     * @param id - ID del evento a actualizar (puede ser id numérico o documentId de Strapi v5)
     * @param eventData - Datos actualizados del evento
     */
    updateEvent(id, eventData) {
        // Transformar los datos al formato que espera Strapi
        const transformedData = transformEventDataForStrapi({...eventData, documentId: id});

        // Log de la transformación para debugging
        if (process.env.NODE_ENV === 'development') {
            logTransformation(eventData, transformedData);
        }

        // Para Strapi v5, usar documentId si está disponible en los datos del evento
        // Si no, usar el id proporcionado
        const updateId = eventData.documentId || id
        const regex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]+$/;
        const isDocumentId = typeof updateId === 'string' && regex.test(updateId)
        const endpoint = isDocumentId
            ? API_ENDPOINTS.EVENTS.BY_DOCUMENT_ID(updateId)
            : API_ENDPOINTS.EVENTS.BY_ID(updateId)

        return ApiService.fetchData<Event>({
            url: endpoint,
            method: 'put',
            data: { data: transformedData } as Record<string, unknown>,
        }).then((res) => transformEventDataFromStrapi(res.data))
    },

    /**
     * Elimina un evento
     * @param id - ID del evento a eliminar (puede ser id numérico o documentId de Strapi v5)
     */
    deleteEvent(id) {
        // Para Strapi v5, usar documentId si está disponible
        const isDocumentId = typeof id === 'string' && id.startsWith('doc-')
        const endpoint = isDocumentId
            ? API_ENDPOINTS.EVENTS.BY_DOCUMENT_ID(id)
            : API_ENDPOINTS.EVENTS.BY_ID(id)

        return ApiService.fetchData<void>({
            url: endpoint,
            method: 'delete',
        }).then((res) => res.data)
    },

    /** Obtiene todos los participantes disponibles */
    getParticipants() {
        return ApiService.fetchData<Participant[]>({
            url: API_ENDPOINTS.EVENTS.PARTICIPANTS,
            method: 'get',
        }).then((res) => res.data)
    },

    /** Obtiene los tipos de eventos */
    getEventTypes() {
        return ApiService.fetchData< EventType[]>({
            url: API_ENDPOINTS.EVENTS.TYPES,
            method: 'get',
        }).then((res) => res?.data?.data)
    },

    /**
     * Importa participantes a un evento
     */
    importParticipants(eventId, participants) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.EVENTS.IMPORT_PARTICIPANTS(eventId),
            method: 'post',
            data: { participants } as Record<string, unknown>,
        }).then(() => true)
    },

    /**
     * Permite que un usuario se registre a sí mismo en un evento
     */
    selfRegisterToEvent(eventId, participantData) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.EVENTS.SELF_REGISTER(eventId),
            method: 'post',
            data: { participantData } as Record<string, unknown>,
        }).then(() => true)
    },

    registerParticipantAndMarkAttendance(eventId, participantData) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.EVENTS.REGISTER_PARTICIPANT_AND_MARK_ATTENDANCE(
                eventId,
            ),
            method: 'post',
            data: { participantData } as Record<string, unknown>,
        }).then(() => true)
    },
    /**
     * Registra asistencia de un participante a un evento
     */
    recordAttendance(eventId, sessionId, participantId, attended, notes) {
        // Para Strapi v5, usar documentId si está disponible
        const isDocumentId = typeof eventId === 'string' && eventId.startsWith('doc-')
        const endpoint = isDocumentId
            ? API_ENDPOINTS.EVENTS.RECORD_ATTENDANCE_BY_DOCUMENT_ID(eventId, sessionId)
            : API_ENDPOINTS.EVENTS.RECORD_ATTENDANCE(eventId, sessionId)

        return ApiService.fetchData<AttendanceRecord>({
            url: endpoint,
            method: 'post',
            data: { participantId, attended, notes } as Record<string, unknown>,
        }).then((res) => res.data)
    },

    /** Obtiene estadísticas para el tablero de eventos */
    getDashboardStats() {
        return ApiService.fetchData<DashboardStats>({
            url: API_ENDPOINTS.EVENTS.DASHBOARD_STATS,
            method: 'get',
        }).then((res) => res.data)
    },

    /** Obtiene la información para filtros jerárquicos */
    getHierarchicalFilterData() {
        return ApiService.fetchData<HierarchicalData[]>({
            url: API_ENDPOINTS.EVENTS.HIERARCHICAL_FILTERS,
            method: 'get',
        }).then((res) => res.data)
    },

    /**
     * Obtiene los eventos a los que está invitado un usuario
     * @param userId - ID del usuario
     */
    getEventsForUser(userId) {
        return ApiService.fetchData<Event[]>({
            url: API_ENDPOINTS.EVENTS.USER_EVENTS(userId),
            method: 'get',
        }).then((res) => res.data)
    },

    /**
     * Registra la justificación de una ausencia
     */
    justifyAbsence(eventId, userId, justification) {
        return ApiService.fetchData({
            url: API_ENDPOINTS.EVENTS.JUSTIFY_ABSENCE(eventId),
            method: 'post',
            data: { userId, justification } as Record<string, unknown>,
        }).then(() => true)
    },

    /**
     * Actualiza el estado de un evento
     */
    updateEventStatus(eventId, status) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.EVENTS.UPDATE_STATUS(eventId),
            method: 'patch',
            data: { status } as Record<string, unknown>,
        }).then(() => true)
    },

    /**
     * Actualiza el estado de una sesión específica
     */
    updateSessionStatus(eventId, sessionId, status) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.EVENTS.UPDATE_SESSION_STATUS(
                eventId,
                sessionId,
            ),
            method: 'patch',
            data: { status } as Record<string, unknown>,
        }).then(() => true)
    },

    /**
     * Obtiene el estado de una sesión específica
     */
    getSessionStatus(eventId, sessionId) {
        return ApiService.fetchData<{ status: string }>({
            url: API_ENDPOINTS.EVENTS.GET_SESSION_STATUS(eventId, sessionId),
            method: 'get',
        }).then((response) => response.status as any)
    },

    /**
     * Completa automáticamente una sesión en modo kiosco
     */
    completeKioskSession(eventId, sessionId) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.EVENTS.COMPLETE_KIOSK_SESSION(
                eventId,
                sessionId,
            ),
            method: 'post',
        }).then(() => true)
    },

    /**
     * Valida la existencia de un participante antes del registro
     */
    validateParticipantExists(eventId, participantData) {
        return ApiService.fetchData<ParticipantValidationResult>({
            url: API_ENDPOINTS.EVENTS.VALIDATE_PARTICIPANT(eventId),
            method: 'post',
            data: { participantData } as Record<string, unknown>,
        }).then((res) => res.data)
    },
}

export default EventsApiService
